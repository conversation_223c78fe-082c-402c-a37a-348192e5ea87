{"timestamp": 1752385947.8429224, "test_results": [{"test": "Config Loading", "success": true, "details": "Configuration loaded successfully", "timestamp": 1752385905.4559782}, {"test": "Model Manager", "success": true, "details": "Response: Please provide me with a prompt to test.  I need a question, task, or instruction to be able to give you a response.\n", "timestamp": 1752385907.6660118}, {"test": "Conversation Manager", "success": true, "details": "Conversation manager initialized", "timestamp": 1752385907.668038}, {"test": "Agent Initialization", "success": true, "details": "Agent initialized successfully", "timestamp": 1752385907.7649622}, {"test": "Message Processing", "success": true, "details": "Result: Okay, I received your \"Hello, test message\".  Is there anything specific you would like me to do with this message?  I'm ready for further instructions.\n", "timestamp": 1752385908.6368952}, {"test": "Autonomous Controller Import", "success": true, "details": "Successfully imported", "timestamp": 1752385908.6368952}, {"test": "Autonomous Framework Import", "success": true, "details": "Framework imported", "timestamp": 1752385908.6368952}, {"test": "Execution Engine Import", "success": true, "details": "Engine imported", "timestamp": 1752385908.6368952}, {"test": "CLI Import", "success": true, "details": "CLI module imported successfully", "timestamp": 1752385908.8210833}, {"test": "CLI Command start", "success": true, "details": "Command parsed: /auto start Create a test function", "timestamp": 1752385908.8210833}, {"test": "CLI Command stop", "success": true, "details": "Command parsed: /auto stop", "timestamp": 1752385908.8210833}, {"test": "CLI Command status", "success": true, "details": "Command parsed: /auto status", "timestamp": 1752385908.8210833}, {"test": "CLI Command pause", "success": true, "details": "Command parsed: /auto pause", "timestamp": 1752385908.8210833}, {"test": "CLI Command resume", "success": true, "details": "Command parsed: /auto resume", "timestamp": 1752385908.8210833}, {"test": "Memory Usage", "success": true, "details": "Memory usage acceptable: 0.00 MB", "timestamp": 1752385909.3630652}, {"test": "Invalid API Key Handling", "success": true, "details": "Error handled gracefully", "timestamp": 1752385909.4709942}, {"test": "Network Timeout Handling", "success": true, "details": "Timeout handled properly", "timestamp": 1752385910.4841964}, {"test": "Memory Limit Handling", "success": true, "details": "Allocated 1000 items", "timestamp": 1752385910.4934442}, {"test": "Invalid Input (NoneType)", "success": true, "details": "Error caught: 1 validation error for Message\ncontent\n  Input should be a valid string [type=string_type, input_value=None, input_type=NoneType]\n    For further information visit https://errors.pydantic.dev/2.11/v/string_type", "timestamp": 1752385910.583706}, {"test": "Invalid Input (str)", "success": true, "details": "Handled: Please provide me with a task or question. I need instructions to be able to help you.\n", "timestamp": 1752385912.808197}, {"test": "Invalid Input (str)", "success": true, "details": "Handled: I'm sorry, but I cannot process your request.  The input consists only of the character 'x' repeated many times.  It does not contain any instructions or information for me to work with.  Please provide a clear and concise task or question.\n", "timestamp": 1752385914.3113956}, {"test": "Invalid Input (dict)", "success": true, "details": "Error caught: 1 validation error for Message\ncontent\n  Input should be a valid string [type=string_type, input_value={'invalid': 'dict'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/string_type", "timestamp": 1752385914.3113956}, {"test": "Invalid Input (int)", "success": true, "details": "Error caught: 1 validation error for Message\ncontent\n  Input should be a valid string [type=string_type, input_value=123, input_type=int]\n    For further information visit https://errors.pydantic.dev/2.11/v/string_type", "timestamp": 1752385914.3113956}, {"test": "Invalid Input (list)", "success": true, "details": "Error caught: 1 validation error for Message\ncontent\n  Input should be a valid string [type=string_type, input_value=[], input_type=list]\n    For further information visit https://errors.pydantic.dev/2.11/v/string_type", "timestamp": 1752385914.3123958}, {"test": "Scenario: Simple Code Generation", "success": true, "details": "Session started: session_1752385914", "timestamp": 1752385919.1805277}, {"test": "Scenario: Research Task", "success": true, "details": "Session started: session_1752385920", "timestamp": 1752385926.3712194}, {"test": "Scenario: Complex Project", "success": true, "details": "Session started: session_1752385927", "timestamp": 1752385933.4973812}, {"test": "Scenario: Data Analysis", "success": true, "details": "Session started: session_1752385934", "timestamp": 1752385939.8345625}, {"test": "Scenario: API Integration", "success": true, "details": "Session started: session_1752385940", "timestamp": 1752385946.8206067}], "errors_found": [], "fixes_applied": [], "statistics": {"total_tests": 29, "passed_tests": 29, "failed_tests": 0, "errors_count": 0, "fixes_count": 0}}