#!/usr/bin/env python3
"""
Debug workspace directory issue
"""

import sys
import os
import traceback
import tempfile
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def debug_workspace_issue():
    """Debug the exact workspace directory issue"""
    print("🔍 Debugging workspace directory issue...")
    
    try:
        # Test 1: Basic imports
        print("\n1. Testing imports...")
        from models import ModelManager
        from conversation import ConversationManager
        print("  ✅ Basic imports successful")
        
        # Test 2: Model manager with test provider
        print("\n2. Testing model manager...")
        model_manager = ModelManager(provider="test", model_name="test-model")
        response = model_manager.generate("Test prompt")
        print(f"  ✅ Model manager working: {response[:50]}...")
        
        # Test 3: Conversation manager
        print("\n3. Testing conversation manager...")
        conv_manager = ConversationManager("./test_history")
        print("  ✅ Conversation manager working")
        
        # Test 4: Agent with different workspace types
        print("\n4. Testing Agent with different workspace types...")
        
        from agent import Agent
        
        # Test with string workspace
        print("  Testing with string workspace...")
        temp_dir_str = tempfile.mkdtemp(prefix="debug_string_")
        try:
            agent1 = Agent(model_manager, conv_manager, temp_dir_str)
            print(f"    ✅ String workspace: {type(agent1.workspace_dir)} = {agent1.workspace_dir}")
        except Exception as e:
            print(f"    ❌ String workspace failed: {e}")
            traceback.print_exc()
        
        # Test with Path workspace
        print("  Testing with Path workspace...")
        temp_dir_path = Path(tempfile.mkdtemp(prefix="debug_path_"))
        try:
            agent2 = Agent(model_manager, conv_manager, temp_dir_path)
            print(f"    ✅ Path workspace: {type(agent2.workspace_dir)} = {agent2.workspace_dir}")
        except Exception as e:
            print(f"    ❌ Path workspace failed: {e}")
            traceback.print_exc()
        
        # Test 5: Autonomous controller initialization
        print("\n5. Testing autonomous controller initialization...")
        try:
            # Use the working agent
            agent = agent2 if 'agent2' in locals() else agent1
            
            # Check if autonomous controller was initialized
            if hasattr(agent, 'autonomous_controller'):
                print(f"    ✅ Autonomous controller exists: {agent.autonomous_controller}")
                
                # Test autonomous status
                status = agent.get_autonomous_status()
                print(f"    ✅ Autonomous status: {status}")
                
            else:
                print("    ❌ Autonomous controller not found")
                
        except Exception as e:
            print(f"    ❌ Autonomous controller failed: {e}")
            traceback.print_exc()
        
        # Test 6: Detailed component check
        print("\n6. Testing individual components...")
        
        try:
            # Test autonomous framework import
            from core.autonomous_agent_framework import TaskPersistenceManager
            
            test_storage_dir = Path(tempfile.mkdtemp(prefix="debug_storage_"))
            persistence_manager = TaskPersistenceManager(test_storage_dir)
            print(f"    ✅ TaskPersistenceManager: {persistence_manager}")
            
        except Exception as e:
            print(f"    ❌ TaskPersistenceManager failed: {e}")
            traceback.print_exc()
        
        try:
            # Test autonomous controller import
            from core.autonomous_agent_controller import AutonomousAgentController
            
            test_workspace = Path(tempfile.mkdtemp(prefix="debug_controller_"))
            controller = AutonomousAgentController(
                model_manager=model_manager,
                conversation_manager=conv_manager,
                workspace_dir=test_workspace,
                tools={}
            )
            print(f"    ✅ AutonomousAgentController: {controller}")
            
        except Exception as e:
            print(f"    ❌ AutonomousAgentController failed: {e}")
            traceback.print_exc()
        
        print("\n🎉 Debug completed!")
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    debug_workspace_issue()
