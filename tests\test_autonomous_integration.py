#!/usr/bin/env python3
"""
Integration tests for the Autonomous AI Agent System.

These tests verify that all components work together correctly:
1. End-to-end autonomous task execution
2. Research engine integration
3. Task persistence and recovery
4. Iterative improvement system
5. Progress monitoring and callbacks
"""

import sys
import os
import time
import tempfile
import shutil
import unittest
from pathlib import Path
from unittest.mock import Mock, MagicMock, patch

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from core.autonomous_agent_controller import AutonomousAgentController
from core.autonomous_agent_framework import AgentMode, TaskStatus
from core.autonomous_execution_engine import AutonomousExecutionEngine
from core.iterative_improvement_system import IterativeImprovementSystem
from models import ModelManager
from conversation import ConversationManager

class TestAutonomousIntegration(unittest.TestCase):
    """Integration tests for autonomous agent system."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary workspace
        self.temp_dir = Path(tempfile.mkdtemp())
        self.workspace_dir = self.temp_dir / "workspace"
        self.workspace_dir.mkdir(parents=True, exist_ok=True)
        
        # Mock model manager
        self.model_manager = Mock(spec=ModelManager)
        self.model_manager.generate.return_value = "Test response from model"
        
        # Mock conversation manager
        self.conversation_manager = Mock(spec=ConversationManager)
        
        # Mock tools
        self.tools = {
            "web": Mock(),
            "search": Mock(),
            "code": Mock(),
            "file": Mock()
        }
        
        # Initialize autonomous controller
        self.controller = AutonomousAgentController(
            model_manager=self.model_manager,
            conversation_manager=self.conversation_manager,
            workspace_dir=self.workspace_dir,
            tools=self.tools
        )
        
        # Test data
        self.test_task = "Create a simple Python function that adds two numbers"
        self.test_constraints = {"max_time": "5m", "language": "python"}
    
    def tearDown(self):
        """Clean up test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_autonomous_session_lifecycle(self):
        """Test complete autonomous session lifecycle."""
        # Start autonomous session
        session_id = self.controller.start_autonomous_session(
            request=self.test_task,
            mode=AgentMode.AUTONOMOUS,
            constraints=self.test_constraints
        )
        
        self.assertIsNotNone(session_id)
        self.assertTrue(session_id.startswith("session_"))
        
        # Check session status
        status = self.controller.get_session_status()
        self.assertTrue(status["active"])
        self.assertEqual(status["session_id"], session_id)
        self.assertEqual(status["mode"], "autonomous")
        
        # Wait briefly for execution to start
        time.sleep(1)
        
        # Stop session
        stop_result = self.controller.stop_autonomous_session()
        self.assertTrue(stop_result)
        
        # Verify session stopped
        status = self.controller.get_session_status()
        self.assertFalse(status["active"])
    
    def test_task_persistence(self):
        """Test task persistence across sessions."""
        # Start session and create task
        session_id = self.controller.start_autonomous_session(
            request=self.test_task,
            mode=AgentMode.AUTONOMOUS
        )
        
        # Wait for task creation
        time.sleep(1)
        
        # Get active tasks
        active_tasks = self.controller.persistence_manager.get_active_tasks()
        self.assertGreater(len(active_tasks), 0)
        
        task_id = active_tasks[0].task_id
        
        # Stop session
        self.controller.stop_autonomous_session()
        
        # Verify task persists
        persisted_task = self.controller.persistence_manager.get_task(task_id)
        self.assertIsNotNone(persisted_task)
        self.assertEqual(persisted_task.description, self.test_task)
    
    def test_research_engine_integration(self):
        """Test research engine integration."""
        # Mock web search results
        self.tools["web"].search.return_value = [
            {"title": "Python Functions", "content": "How to create functions in Python"},
            {"title": "Addition in Python", "content": "Adding numbers in Python"}
        ]
        
        # Start session with research-heavy task
        research_task = "Research Python best practices and create a function"
        session_id = self.controller.start_autonomous_session(
            request=research_task,
            mode=AgentMode.AUTONOMOUS
        )
        
        # Wait for research to potentially occur
        time.sleep(2)
        
        # Check research history
        research_history = self.controller.get_research_history()
        
        # Stop session
        self.controller.stop_autonomous_session()
        
        # Research may or may not have occurred depending on execution path
        # This test mainly verifies the integration doesn't crash
        self.assertIsInstance(research_history, list)
    
    def test_progress_monitoring(self):
        """Test progress monitoring and callbacks."""
        progress_updates = []
        status_updates = []
        completion_events = []
        
        def progress_callback(data):
            progress_updates.append(data)
        
        def status_callback(data):
            status_updates.append(data)
        
        def completion_callback(data):
            completion_events.append(data)
        
        # Set callbacks
        self.controller.set_progress_callback(progress_callback)
        self.controller.set_status_callback(status_callback)
        self.controller.set_completion_callback(completion_callback)
        
        # Start session
        session_id = self.controller.start_autonomous_session(
            request=self.test_task,
            mode=AgentMode.AUTONOMOUS
        )
        
        # Wait for some execution
        time.sleep(2)
        
        # Stop session
        self.controller.stop_autonomous_session()
        
        # Verify callbacks were called
        self.assertGreater(len(status_updates), 0)
        
        # Check for session start event
        session_start_events = [u for u in status_updates if u.get("type") == "session_started"]
        self.assertGreater(len(session_start_events), 0)
    
    def test_semi_autonomous_mode(self):
        """Test semi-autonomous mode with decision points."""
        decision_points = []
        
        def decision_callback(data):
            decision_points.append(data)
        
        # Set decision callback
        self.controller.execution_engine.set_decision_callback(decision_callback)
        
        # Start semi-autonomous session
        session_id = self.controller.start_autonomous_session(
            request=self.test_task,
            mode=AgentMode.SEMI_AUTONOMOUS
        )
        
        # Wait for execution
        time.sleep(2)
        
        # Stop session
        self.controller.stop_autonomous_session()
        
        # Verify semi-autonomous mode was used
        status = self.controller.get_session_status()
        # Session should be stopped by now, but we can check history
        session_history = self.controller.get_session_history()
        if session_history:
            latest_session = session_history[-1]
            self.assertEqual(latest_session["mode"], "semi_autonomous")
    
    def test_error_handling_and_recovery(self):
        """Test error handling and recovery mechanisms."""
        # Mock model to sometimes fail
        call_count = 0
        def mock_generate(prompt):
            nonlocal call_count
            call_count += 1
            if call_count == 2:  # Fail on second call
                raise Exception("Simulated model failure")
            return "Test response"
        
        self.model_manager.generate.side_effect = mock_generate
        
        # Start session
        session_id = self.controller.start_autonomous_session(
            request=self.test_task,
            mode=AgentMode.AUTONOMOUS
        )
        
        # Wait for execution and potential error handling
        time.sleep(3)
        
        # Stop session
        self.controller.stop_autonomous_session()
        
        # Verify system handled the error gracefully
        # (didn't crash and session could be stopped)
        self.assertTrue(True)  # If we get here, error handling worked
    
    def test_task_pause_resume(self):
        """Test task pause and resume functionality."""
        # Start session
        session_id = self.controller.start_autonomous_session(
            request=self.test_task,
            mode=AgentMode.AUTONOMOUS
        )
        
        # Wait for task to start
        time.sleep(1)
        
        # Pause task
        pause_result = self.controller.pause_current_task()
        
        # Wait a bit
        time.sleep(1)
        
        # Resume task
        resume_result = self.controller.resume_current_task()
        
        # Wait a bit more
        time.sleep(1)
        
        # Stop session
        self.controller.stop_autonomous_session()
        
        # Verify pause/resume operations
        # Results may vary depending on timing and task state
        self.assertIsInstance(pause_result, bool)
        self.assertIsInstance(resume_result, bool)
    
    def test_iterative_improvement_integration(self):
        """Test iterative improvement system integration."""
        # Create improvement system
        improvement_system = IterativeImprovementSystem(
            model_manager=self.model_manager,
            tools=self.tools,
            workspace_dir=self.workspace_dir
        )
        
        # Test content to improve
        test_code = """
def add(a, b):
    return a + b
"""
        
        # Run iterative improvement
        result = improvement_system.improve_iteratively(
            content=test_code,
            content_type="python",
            success_criteria=["syntactically_correct", "well_documented", "tested"]
        )
        
        # Verify improvement result
        self.assertIsNotNone(result)
        self.assertEqual(result.original_content, test_code)
        self.assertIsInstance(result.improved_content, str)
        self.assertIsInstance(result.overall_improvement_score, float)
        self.assertIsInstance(result.success, bool)
    
    def test_concurrent_sessions(self):
        """Test handling of concurrent session attempts."""
        # Start first session
        session_id1 = self.controller.start_autonomous_session(
            request=self.test_task,
            mode=AgentMode.AUTONOMOUS
        )
        
        # Try to start second session (should fail or return same ID)
        session_id2 = self.controller.start_autonomous_session(
            request="Another task",
            mode=AgentMode.AUTONOMOUS
        )
        
        # Should either be the same session or second should fail
        self.assertEqual(session_id1, session_id2)
        
        # Stop session
        self.controller.stop_autonomous_session()
    
    def test_workspace_isolation(self):
        """Test workspace isolation and file operations."""
        # Start session with file creation task
        file_task = "Create a Python file with a hello world function"
        session_id = self.controller.start_autonomous_session(
            request=file_task,
            mode=AgentMode.AUTONOMOUS
        )
        
        # Wait for execution
        time.sleep(2)
        
        # Stop session
        self.controller.stop_autonomous_session()
        
        # Verify workspace exists and is isolated
        self.assertTrue(self.workspace_dir.exists())
        self.assertTrue(self.workspace_dir.is_dir())
        
        # Check for autonomous data directory
        autonomous_data_dir = self.workspace_dir / "autonomous_data"
        if autonomous_data_dir.exists():
            self.assertTrue(autonomous_data_dir.is_dir())

class TestAutonomousComponents(unittest.TestCase):
    """Test individual autonomous components."""
    
    def setUp(self):
        """Set up component tests."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.model_manager = Mock(spec=ModelManager)
        self.model_manager.generate.return_value = "Test response"
        
    def tearDown(self):
        """Clean up component tests."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_iterative_improvement_system(self):
        """Test iterative improvement system independently."""
        tools = {"code": Mock()}
        tools["code"].execute.return_value = "Execution successful"
        
        improvement_system = IterativeImprovementSystem(
            model_manager=self.model_manager,
            tools=tools,
            workspace_dir=self.temp_dir
        )
        
        # Test improvement
        test_content = "def test(): pass"
        result = improvement_system.improve_iteratively(
            content=test_content,
            content_type="python",
            success_criteria=["works", "documented"]
        )
        
        self.assertIsNotNone(result)
        self.assertIsInstance(result.execution_time, float)
        self.assertGreaterEqual(result.execution_time, 0)
    
    def test_improvement_statistics(self):
        """Test improvement statistics tracking."""
        tools = {"code": Mock()}
        improvement_system = IterativeImprovementSystem(
            model_manager=self.model_manager,
            tools=tools,
            workspace_dir=self.temp_dir
        )
        
        # Run multiple improvements
        for i in range(3):
            improvement_system.improve_iteratively(
                content=f"def test{i}(): pass",
                content_type="python",
                success_criteria=["works"]
            )
        
        # Get statistics
        stats = improvement_system.get_improvement_statistics()
        
        self.assertEqual(stats["total_iterations"], 3)
        self.assertIn("success_rate", stats)
        self.assertIn("average_improvement_score", stats)

def run_integration_tests():
    """Run all integration tests."""
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add integration tests
    suite.addTest(unittest.makeSuite(TestAutonomousIntegration))
    suite.addTest(unittest.makeSuite(TestAutonomousComponents))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
