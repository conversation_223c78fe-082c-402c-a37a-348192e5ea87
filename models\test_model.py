"""
Test Model for testing and development purposes.
"""

import logging
import time
import random
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class TestModel:
    """Mock model for testing purposes."""
    
    def __init__(self, model_name: str = "test-model", **kwargs):
        """Initialize test model."""
        self.model_name = model_name
        self.kwargs = kwargs
        self.call_count = 0
        
        logger.info(f"TestModel initialized: {model_name}")
    
    def generate(self, prompt: str, system_prompt: Optional[str] = None,
                images: Optional[List] = None, conversation_history: Optional[List] = None, **kwargs) -> str:
        """Generate a test response."""
        self.call_count += 1

        # Simulate some processing time
        time.sleep(0.1)
        
        # Generate different types of responses based on prompt
        prompt_lower = prompt.lower()
        
        if "error" in prompt_lower or "fail" in prompt_lower:
            return "Test error response: This is a simulated error for testing."
        
        elif "code" in prompt_lower or "function" in prompt_lower:
            return """Here's a test code response:

```python
def test_function(x, y):
    \"\"\"Test function for demonstration.\"\"\"
    return x + y

# Example usage
result = test_function(5, 3)
print(f"Result: {result}")
```

This is a mock code generation for testing purposes."""
        
        elif "research" in prompt_lower or "analyze" in prompt_lower:
            return """Test research response:

Based on the analysis, here are the key findings:

1. **Primary Point**: This is a mock research finding for testing
2. **Secondary Analysis**: Additional test data and insights
3. **Conclusions**: Test conclusions and recommendations

Sources: Mock sources for testing purposes
Confidence: 85% (simulated)"""
        
        elif "plan" in prompt_lower or "steps" in prompt_lower:
            return """Test planning response:

Here's a step-by-step plan:

1. **Initial Setup**: Prepare the test environment
2. **Implementation**: Execute the main test logic
3. **Validation**: Verify the test results
4. **Cleanup**: Clean up test resources
5. **Reporting**: Generate test reports

This is a mock planning response for testing."""
        
        elif "json" in prompt_lower:
            return """{
    "test_response": true,
    "message": "This is a mock JSON response",
    "data": {
        "items": ["test1", "test2", "test3"],
        "count": 3,
        "status": "success"
    },
    "timestamp": "2024-01-01T00:00:00Z"
}"""
        
        else:
            # Default response
            responses = [
                f"Test response for prompt: {prompt[:50]}...",
                f"Mock AI response #{self.call_count}: This is a simulated response for testing purposes.",
                f"Generated test content based on: {prompt[:30]}... (Call #{self.call_count})",
                f"Test model output: Processed your request about '{prompt[:40]}...' successfully.",
                f"Simulated response #{self.call_count}: This demonstrates the test model functionality."
            ]
            
            return random.choice(responses)
    
    def is_ready(self) -> bool:
        """Check if model is ready."""
        return True
    
    def get_stats(self) -> Dict[str, Any]:
        """Get model statistics."""
        return {
            "model_name": self.model_name,
            "call_count": self.call_count,
            "status": "ready",
            "type": "test_model"
        }
    
    def reset_stats(self):
        """Reset model statistics."""
        self.call_count = 0
        logger.info("TestModel stats reset")
    
    def simulate_error(self, error_type: str = "general"):
        """Simulate different types of errors for testing."""
        error_types = {
            "api": "API key invalid or expired",
            "network": "Network connection timeout",
            "rate_limit": "Rate limit exceeded",
            "general": "General model error"
        }
        
        error_msg = error_types.get(error_type, error_types["general"])
        raise Exception(f"Simulated {error_type} error: {error_msg}")
    
    def __str__(self):
        return f"TestModel(name={self.model_name}, calls={self.call_count})"
    
    def __repr__(self):
        return self.__str__()

class MockModelManager:
    """Mock model manager for testing."""
    
    def __init__(self, provider: str = "test", model_name: str = "test-model", **kwargs):
        """Initialize mock model manager."""
        self.provider = provider
        self.model_name = model_name
        self.model = TestModel(model_name, **kwargs)
        
        logger.info(f"MockModelManager initialized: {provider}/{model_name}")
    
    def generate(self, prompt: str, **kwargs) -> str:
        """Generate response using test model."""
        return self.model.generate(prompt, **kwargs)
    
    def is_ready(self) -> bool:
        """Check if model manager is ready."""
        return self.model.is_ready()
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information."""
        return {
            "provider": self.provider,
            "model_name": self.model_name,
            "stats": self.model.get_stats(),
            "ready": self.is_ready()
        }
    
    def reset(self):
        """Reset model manager."""
        self.model.reset_stats()
        logger.info("MockModelManager reset")
