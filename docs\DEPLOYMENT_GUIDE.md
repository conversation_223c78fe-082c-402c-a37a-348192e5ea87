# Autonomous AI Agent System - Deployment Guide

## Overview

This guide covers the deployment and configuration of the Autonomous AI Agent System for production use.

## Prerequisites

### System Requirements

- **Operating System**: Linux (Ubuntu 20.04+), macOS (10.15+), or Windows 10+
- **Python**: 3.8 or higher
- **Memory**: Minimum 4GB RAM, recommended 8GB+
- **Storage**: Minimum 2GB free space for workspace and logs
- **Network**: Stable internet connection for AI model APIs and web research

### Dependencies

- Python packages (see `requirements.txt`)
- AI model API access (Gemini, OpenAI, etc.)
- Optional: Docker for containerized deployment

## Installation

### Standard Installation

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd autonomous-ai-agent
   ```

2. **Create Virtual Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

### Docker Installation

1. **Build Docker Image**
   ```bash
   docker build -t autonomous-ai-agent .
   ```

2. **Run Container**
   ```bash
   docker run -d \
     --name ai-agent \
     -v $(pwd)/workspace:/app/workspace \
     -v $(pwd)/config:/app/config \
     -e GEMINI_API_KEY=your_api_key \
     -p 8000:8000 \
     autonomous-ai-agent
   ```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```bash
# AI Model Configuration
GEMINI_API_KEY=your_gemini_api_key
OPENAI_API_KEY=your_openai_api_key
MODEL_PROVIDER=gemini
MODEL_NAME=gemini-pro
MODEL_TEMPERATURE=0.7
MODEL_MAX_TOKENS=4096

# Agent Configuration
WORKSPACE_DIR=./workspace
HISTORY_DIR=./history
LOG_LEVEL=INFO
LOG_FILE=./logs/agent.log

# Autonomous Mode Settings
MAX_EXECUTION_TIME=7200  # 2 hours in seconds
CHECKPOINT_INTERVAL=600   # 10 minutes in seconds
RETRY_LIMIT=3
CONFIDENCE_THRESHOLD=0.7

# Research Configuration
RESEARCH_DEPTH=comprehensive
MAX_RESEARCH_SOURCES=20
RESEARCH_TIMEOUT=120

# Security Settings
ENABLE_CODE_EXECUTION=true
SANDBOX_MODE=true
ALLOWED_DOMAINS=github.com,stackoverflow.com,docs.python.org

# Performance Settings
MAX_CONCURRENT_TASKS=3
THREAD_POOL_SIZE=10
MEMORY_LIMIT=2048  # MB
```

### Configuration Files

#### `config/agent_config.yaml`

```yaml
agent:
  provider: gemini
  model: gemini-pro
  temperature: 0.7
  max_tokens: 4096
  timeout: 30

autonomous:
  max_execution_time: 7200
  checkpoint_interval: 600
  retry_limit: 3
  confidence_threshold: 0.7
  modes:
    - autonomous
    - semi_autonomous
    - interactive

research:
  depth: comprehensive
  max_sources: 20
  timeout: 120
  cache_enabled: true
  cache_ttl: 3600

improvement:
  max_iterations: 5
  improvement_threshold: 0.1
  validation_timeout: 30

workspace:
  base_dir: ./workspace
  temp_dir: ./temp
  logs_dir: ./logs
  max_size: 1024  # MB

security:
  sandbox_mode: true
  code_execution: true
  allowed_domains:
    - github.com
    - stackoverflow.com
    - docs.python.org
  blocked_commands:
    - rm -rf
    - sudo
    - chmod 777
```

## Deployment Options

### 1. Standalone Server

Deploy as a standalone Python application:

```bash
# Start the agent server
python main.py --server --port 8000 --host 0.0.0.0

# Or use gunicorn for production
gunicorn -w 4 -b 0.0.0.0:8000 server:app
```

### 2. Docker Deployment

#### Dockerfile

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p workspace logs history

# Set permissions
RUN chmod +x scripts/start.sh

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# Start application
CMD ["./scripts/start.sh"]
```

#### Docker Compose

```yaml
version: '3.8'

services:
  ai-agent:
    build: .
    container_name: autonomous-ai-agent
    ports:
      - "8000:8000"
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - MODEL_PROVIDER=gemini
      - LOG_LEVEL=INFO
    volumes:
      - ./workspace:/app/workspace
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:alpine
    container_name: ai-agent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: ai-agent-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - ai-agent
    restart: unless-stopped

volumes:
  redis_data:
```

### 3. Kubernetes Deployment

#### Deployment YAML

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: autonomous-ai-agent
  labels:
    app: ai-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-agent
  template:
    metadata:
      labels:
        app: ai-agent
    spec:
      containers:
      - name: ai-agent
        image: autonomous-ai-agent:latest
        ports:
        - containerPort: 8000
        env:
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-agent-secrets
              key: gemini-api-key
        - name: MODEL_PROVIDER
          value: "gemini"
        - name: LOG_LEVEL
          value: "INFO"
        volumeMounts:
        - name: workspace
          mountPath: /app/workspace
        - name: config
          mountPath: /app/config
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: workspace
        persistentVolumeClaim:
          claimName: ai-agent-workspace
      - name: config
        configMap:
          name: ai-agent-config
---
apiVersion: v1
kind: Service
metadata:
  name: ai-agent-service
spec:
  selector:
    app: ai-agent
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
```

## Security Configuration

### API Key Management

1. **Environment Variables** (Development)
   ```bash
   export GEMINI_API_KEY=your_api_key
   ```

2. **Secrets Management** (Production)
   ```bash
   # Using Kubernetes secrets
   kubectl create secret generic ai-agent-secrets \
     --from-literal=gemini-api-key=your_api_key
   
   # Using Docker secrets
   echo "your_api_key" | docker secret create gemini_api_key -
   ```

### Sandbox Configuration

Enable sandboxing for code execution:

```python
# In config/security.yaml
sandbox:
  enabled: true
  timeout: 30
  memory_limit: 512  # MB
  cpu_limit: 1.0
  network_access: false
  file_system_access: restricted
  allowed_modules:
    - os
    - sys
    - json
    - requests
  blocked_modules:
    - subprocess
    - eval
    - exec
```

### Network Security

Configure firewall rules:

```bash
# Allow only necessary ports
sudo ufw allow 8000/tcp  # Agent API
sudo ufw allow 22/tcp    # SSH
sudo ufw enable
```

## Monitoring and Logging

### Logging Configuration

```python
# In config/logging.yaml
version: 1
formatters:
  default:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: default
    stream: ext://sys.stdout
  
  file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/agent.log
    maxBytes: 10485760  # 10MB
    backupCount: 5

loggers:
  core.autonomous_agent_controller:
    level: DEBUG
    handlers: [console, file]
  core.autonomous_execution_engine:
    level: DEBUG
    handlers: [console, file]
  core.autonomous_research_engine:
    level: INFO
    handlers: [console, file]

root:
  level: INFO
  handlers: [console, file]
```

### Health Checks

Implement health check endpoints:

```python
# In server.py
@app.route('/health')
def health_check():
    return {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0',
        'autonomous_available': agent.get_autonomous_status()['available']
    }

@app.route('/ready')
def readiness_check():
    # Check if all components are ready
    checks = {
        'model_manager': model_manager.is_ready(),
        'autonomous_controller': agent._has_autonomous_controller,
        'workspace': workspace_dir.exists()
    }
    
    all_ready = all(checks.values())
    
    return {
        'ready': all_ready,
        'checks': checks
    }, 200 if all_ready else 503
```

### Metrics Collection

```python
# Prometheus metrics example
from prometheus_client import Counter, Histogram, Gauge

# Define metrics
autonomous_sessions_total = Counter('autonomous_sessions_total', 'Total autonomous sessions')
task_execution_duration = Histogram('task_execution_duration_seconds', 'Task execution time')
active_tasks_gauge = Gauge('active_tasks', 'Number of active tasks')

# In your code
autonomous_sessions_total.inc()
task_execution_duration.observe(execution_time)
active_tasks_gauge.set(len(active_tasks))
```

## Performance Optimization

### Resource Limits

```yaml
# In docker-compose.yml
services:
  ai-agent:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

### Caching Configuration

```python
# Redis caching for research results
REDIS_CONFIG = {
    'host': 'localhost',
    'port': 6379,
    'db': 0,
    'decode_responses': True,
    'socket_timeout': 5,
    'socket_connect_timeout': 5,
    'retry_on_timeout': True
}

# Cache TTL settings
CACHE_TTL = {
    'research_results': 3600,  # 1 hour
    'model_responses': 1800,   # 30 minutes
    'task_analysis': 7200      # 2 hours
}
```

## Backup and Recovery

### Data Backup

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backups/ai-agent/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup workspace
tar -czf "$BACKUP_DIR/workspace.tar.gz" workspace/

# Backup configuration
cp -r config/ "$BACKUP_DIR/"

# Backup logs (last 7 days)
find logs/ -name "*.log" -mtime -7 -exec cp {} "$BACKUP_DIR/" \;

# Backup database (if using)
# pg_dump autonomous_agent > "$BACKUP_DIR/database.sql"

echo "Backup completed: $BACKUP_DIR"
```

### Recovery Procedures

```bash
#!/bin/bash
# restore.sh

BACKUP_DIR="$1"

if [ -z "$BACKUP_DIR" ]; then
    echo "Usage: $0 <backup_directory>"
    exit 1
fi

# Stop services
docker-compose down

# Restore workspace
tar -xzf "$BACKUP_DIR/workspace.tar.gz"

# Restore configuration
cp -r "$BACKUP_DIR/config/" .

# Restart services
docker-compose up -d

echo "Recovery completed from: $BACKUP_DIR"
```

## Troubleshooting

### Common Issues

1. **Agent won't start**
   - Check API keys and model availability
   - Verify workspace permissions
   - Check log files for errors

2. **High memory usage**
   - Adjust model parameters
   - Implement result caching
   - Limit concurrent tasks

3. **Slow performance**
   - Optimize model calls
   - Use faster storage
   - Increase resource limits

### Debug Commands

```bash
# Check agent status
curl http://localhost:8000/health

# View logs
docker logs autonomous-ai-agent

# Check resource usage
docker stats autonomous-ai-agent

# Test autonomous functionality
python test_autonomous_agent.py
```

## Maintenance

### Regular Tasks

1. **Log Rotation**: Configure automatic log rotation
2. **Cache Cleanup**: Clear old cache entries
3. **Backup Verification**: Test backup and restore procedures
4. **Security Updates**: Keep dependencies updated
5. **Performance Monitoring**: Monitor resource usage and response times

### Update Procedures

```bash
# Update to new version
git pull origin main
pip install -r requirements.txt
docker-compose build
docker-compose up -d
```

This deployment guide provides comprehensive instructions for deploying the Autonomous AI Agent System in various environments with proper security, monitoring, and maintenance procedures.
