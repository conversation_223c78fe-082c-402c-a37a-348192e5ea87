"""
Semantic Indexer for the AI Code Assistant.

This module builds a semantic code indexing system for instant codebase navigation
and intelligent code search that understands context and intent.
"""

import os
import json
import time
import logging
import threading
import hashlib
from typing import Dict, List, Optional, Any, Union, Tuple, Set
from dataclasses import dataclass, asdict
from pathlib import Path
import ast
import re
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class CodeSymbol:
    """Represents a code symbol (function, class, variable, etc.)."""
    name: str
    type: str  # function, class, variable, method, property
    file_path: str
    line_number: int
    column_number: int
    scope: str
    signature: Optional[str]
    docstring: Optional[str]
    dependencies: List[str]
    usages: List[Tuple[str, int]]  # (file_path, line_number)
    complexity_score: float
    semantic_tags: List[str]

@dataclass
class CodeBlock:
    """Represents a semantic code block."""
    id: str
    content: str
    file_path: str
    start_line: int
    end_line: int
    block_type: str  # function, class, module, etc.
    symbols: List[CodeSymbol]
    imports: List[str]
    exports: List[str]
    semantic_embedding: Optional[List[float]]
    intent_tags: List[str]

@dataclass
class SearchResult:
    """Represents a search result."""
    symbol: Optional[CodeSymbol]
    block: Optional[CodeBlock]
    relevance_score: float
    context_match: str
    reasoning: List[str]

class SemanticIndexer:
    """Semantic code indexing system for intelligent code search and navigation."""

    def __init__(self, workspace_dir: Path, model_manager=None):
        """Initialize the semantic indexer.
        
        Args:
            workspace_dir: The workspace directory
            model_manager: Optional model manager for semantic embeddings
        """
        self.workspace_dir = workspace_dir
        self.model_manager = model_manager
        self.symbols: Dict[str, CodeSymbol] = {}
        self.blocks: Dict[str, CodeBlock] = {}
        self.file_hashes: Dict[str, str] = {}
        self.dependency_graph: Dict[str, Set[str]] = defaultdict(set)
        self.reverse_dependency_graph: Dict[str, Set[str]] = defaultdict(set)
        self.semantic_clusters: Dict[str, List[str]] = {}
        self.lock = threading.RLock()
        
        # Language-specific parsers
        self.language_parsers = {
            "python": self._parse_python_file,
            "javascript": self._parse_javascript_file,
            "typescript": self._parse_typescript_file,
            "java": self._parse_java_file,
            "cpp": self._parse_cpp_file,
            "c": self._parse_c_file,
            "go": self._parse_go_file,
            "rust": self._parse_rust_file,
        }
        
        # File extensions mapping
        self.file_extensions = {
            ".py": "python",
            ".js": "javascript",
            ".ts": "typescript",
            ".jsx": "javascript",
            ".tsx": "typescript",
            ".java": "java",
            ".cpp": "cpp",
            ".cc": "cpp",
            ".cxx": "cpp",
            ".c": "c",
            ".h": "c",
            ".hpp": "cpp",
            ".go": "go",
            ".rs": "rust",
            ".cs": "csharp",
            ".rb": "ruby",
            ".php": "php",
            ".sql": "sql",
            ".html": "html",
            ".css": "css",
            ".json": "json",
            ".yaml": "yaml",
            ".toml": "toml",
            ".ini": "ini",
            ".bash": "bash",
            ".ps1": "powershell",
            ".r": "r",
            ".swift": "swift",
        }

    def index_codebase(self, force_reindex: bool = False) -> Dict[str, Any]:
        """Index the entire codebase.
        
        Args:
            force_reindex: Force reindexing even if files haven't changed
            
        Returns:
            Indexing statistics
        """
        with self.lock:
            logger.info("Starting codebase indexing")
            start_time = time.time()
            
            stats = {
                "files_processed": 0,
                "symbols_found": 0,
                "blocks_created": 0,
                "dependencies_mapped": 0,
                "errors": [],
            }
            
            # Find all code files
            code_files = self._find_code_files()
            
            for file_path in code_files:
                try:
                    if self._should_reindex_file(file_path, force_reindex):
                        self._index_file(file_path, stats)
                        stats["files_processed"] += 1
                except Exception as e:
                    error_msg = f"Error indexing {file_path}: {e}"
                    logger.error(error_msg)
                    stats["errors"].append(error_msg)
            
            # Build dependency graph
            self._build_dependency_graph()
            
            # Create semantic clusters
            if self.model_manager:
                self._create_semantic_clusters()
            
            execution_time = time.time() - start_time
            logger.info(f"Indexing completed in {execution_time:.2f}s: {stats}")
            
            return stats

    def search_semantic(self, query: str, context: Optional[str] = None, 
                       max_results: int = 10) -> List[SearchResult]:
        """Perform semantic search on the codebase.
        
        Args:
            query: The search query
            context: Optional context for the search
            max_results: Maximum number of results to return
            
        Returns:
            List of search results ordered by relevance
        """
        with self.lock:
            logger.info(f"Performing semantic search: {query}")
            
            results = []
            
            # Search symbols
            symbol_results = self._search_symbols(query, context)
            results.extend(symbol_results)
            
            # Search blocks
            block_results = self._search_blocks(query, context)
            results.extend(block_results)
            
            # Sort by relevance score
            results.sort(key=lambda r: r.relevance_score, reverse=True)
            
            return results[:max_results]

    def find_symbol(self, symbol_name: str, symbol_type: Optional[str] = None) -> List[CodeSymbol]:
        """Find symbols by name and optionally type.
        
        Args:
            symbol_name: The symbol name to search for
            symbol_type: Optional symbol type filter
            
        Returns:
            List of matching symbols
        """
        with self.lock:
            matches = []
            
            for symbol in self.symbols.values():
                if symbol.name == symbol_name:
                    if symbol_type is None or symbol.type == symbol_type:
                        matches.append(symbol)
            
            return matches

    def get_symbol_dependencies(self, symbol_name: str) -> Dict[str, Any]:
        """Get dependencies for a symbol.
        
        Args:
            symbol_name: The symbol name
            
        Returns:
            Dictionary with dependency information
        """
        with self.lock:
            dependencies = {
                "direct_dependencies": [],
                "reverse_dependencies": [],
                "dependency_chain": [],
                "circular_dependencies": [],
            }
            
            if symbol_name in self.dependency_graph:
                dependencies["direct_dependencies"] = list(self.dependency_graph[symbol_name])
            
            if symbol_name in self.reverse_dependency_graph:
                dependencies["reverse_dependencies"] = list(self.reverse_dependency_graph[symbol_name])
            
            # Find dependency chain
            dependencies["dependency_chain"] = self._find_dependency_chain(symbol_name)
            
            # Check for circular dependencies
            dependencies["circular_dependencies"] = self._find_circular_dependencies(symbol_name)
            
            return dependencies

    def get_code_context(self, file_path: str, line_number: int, 
                        context_lines: int = 5) -> Dict[str, Any]:
        """Get code context around a specific location.
        
        Args:
            file_path: The file path
            line_number: The line number
            context_lines: Number of context lines to include
            
        Returns:
            Dictionary with context information
        """
        context = {
            "file_path": file_path,
            "line_number": line_number,
            "context_lines": [],
            "symbols_in_context": [],
            "blocks_in_context": [],
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            start_line = max(0, line_number - context_lines - 1)
            end_line = min(len(lines), line_number + context_lines)
            
            for i in range(start_line, end_line):
                context["context_lines"].append({
                    "line_number": i + 1,
                    "content": lines[i].rstrip(),
                    "is_target": i + 1 == line_number,
                })
            
            # Find symbols in context
            for symbol in self.symbols.values():
                if (symbol.file_path == file_path and 
                    start_line <= symbol.line_number <= end_line):
                    context["symbols_in_context"].append(symbol)
            
            # Find blocks in context
            for block in self.blocks.values():
                if (block.file_path == file_path and 
                    block.start_line <= line_number <= block.end_line):
                    context["blocks_in_context"].append(block)
        
        except Exception as e:
            logger.error(f"Error getting context for {file_path}:{line_number}: {e}")
        
        return context

    def _find_code_files(self) -> List[Path]:
        """Find all code files in the workspace."""
        code_files = []
        
        for root, dirs, files in os.walk(self.workspace_dir):
            # Skip common ignore directories
            dirs[:] = [d for d in dirs if d not in {
                'node_modules', '.git', '__pycache__', '.venv', 'venv',
                'build', 'dist', 'target', '.idea', '.vscode'
            }]
            
            for file in files:
                file_path = Path(root) / file
                if file_path.suffix in self.file_extensions:
                    code_files.append(file_path)
        
        return code_files

    def _should_reindex_file(self, file_path: Path, force_reindex: bool) -> bool:
        """Check if a file should be reindexed."""
        if force_reindex:
            return True
        
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                current_hash = hashlib.md5(content).hexdigest()
            
            stored_hash = self.file_hashes.get(str(file_path))
            
            if stored_hash != current_hash:
                self.file_hashes[str(file_path)] = current_hash
                return True
            
            return False
        
        except Exception:
            return True

    def _index_file(self, file_path: Path, stats: Dict[str, Any]):
        """Index a single file."""
        language = self.file_extensions.get(file_path.suffix)
        if not language or language not in self.language_parsers:
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse the file
            parser = self.language_parsers[language]
            symbols, blocks = parser(content, str(file_path))
            
            # Store symbols
            for symbol in symbols:
                symbol_id = f"{symbol.file_path}:{symbol.name}:{symbol.line_number}"
                self.symbols[symbol_id] = symbol
                stats["symbols_found"] += 1
            
            # Store blocks
            for block in blocks:
                self.blocks[block.id] = block
                stats["blocks_created"] += 1
        
        except Exception as e:
            logger.error(f"Error parsing {file_path}: {e}")

    def _parse_python_file(self, content: str, file_path: str) -> Tuple[List[CodeSymbol], List[CodeBlock]]:
        """Parse a Python file."""
        symbols = []
        blocks = []
        
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    symbol = CodeSymbol(
                        name=node.name,
                        type="function",
                        file_path=file_path,
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        scope=self._get_scope(node, tree),
                        signature=self._get_function_signature(node),
                        docstring=ast.get_docstring(node),
                        dependencies=self._extract_dependencies(node),
                        usages=[],
                        complexity_score=self._calculate_complexity(node),
                        semantic_tags=self._extract_semantic_tags(node, content)
                    )
                    symbols.append(symbol)
                    
                    # Create block for function
                    block = CodeBlock(
                        id=f"{file_path}:{node.name}:{node.lineno}",
                        content=ast.get_source_segment(content, node) or "",
                        file_path=file_path,
                        start_line=node.lineno,
                        end_line=getattr(node, 'end_lineno', node.lineno),
                        block_type="function",
                        symbols=[symbol],
                        imports=[],
                        exports=[],
                        semantic_embedding=None,
                        intent_tags=[]
                    )
                    blocks.append(block)
                
                elif isinstance(node, ast.ClassDef):
                    symbol = CodeSymbol(
                        name=node.name,
                        type="class",
                        file_path=file_path,
                        line_number=node.lineno,
                        column_number=node.col_offset,
                        scope=self._get_scope(node, tree),
                        signature=f"class {node.name}",
                        docstring=ast.get_docstring(node),
                        dependencies=self._extract_dependencies(node),
                        usages=[],
                        complexity_score=self._calculate_complexity(node),
                        semantic_tags=self._extract_semantic_tags(node, content)
                    )
                    symbols.append(symbol)
        
        except SyntaxError as e:
            logger.warning(f"Syntax error in {file_path}: {e}")
        
        return symbols, blocks

    def _parse_javascript_file(self, content: str, file_path: str) -> Tuple[List[CodeSymbol], List[CodeBlock]]:
        """Parse a JavaScript file."""
        symbols = []
        blocks = []
        
        # Simple regex-based parsing for JavaScript
        # In production, you'd use a proper JavaScript parser
        
        # Find function declarations
        function_pattern = r'function\s+(\w+)\s*\([^)]*\)\s*\{'
        for match in re.finditer(function_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            symbol = CodeSymbol(
                name=match.group(1),
                type="function",
                file_path=file_path,
                line_number=line_number,
                column_number=match.start() - content.rfind('\n', 0, match.start()),
                scope="global",
                signature=match.group(0),
                docstring=None,
                dependencies=[],
                usages=[],
                complexity_score=0.5,
                semantic_tags=[]
            )
            symbols.append(symbol)
        
        # Find class declarations
        class_pattern = r'class\s+(\w+)\s*\{'
        for match in re.finditer(class_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            symbol = CodeSymbol(
                name=match.group(1),
                type="class",
                file_path=file_path,
                line_number=line_number,
                column_number=match.start() - content.rfind('\n', 0, match.start()),
                scope="global",
                signature=match.group(0),
                docstring=None,
                dependencies=[],
                usages=[],
                complexity_score=0.5,
                semantic_tags=[]
            )
            symbols.append(symbol)
        
        return symbols, blocks

    def _parse_typescript_file(self, content: str, file_path: str) -> Tuple[List[CodeSymbol], List[CodeBlock]]:
        """Parse a TypeScript file."""
        # For now, use JavaScript parser as base
        return self._parse_javascript_file(content, file_path)

    def _parse_java_file(self, content: str, file_path: str) -> Tuple[List[CodeSymbol], List[CodeBlock]]:
        """Parse a Java file."""
        symbols = []
        blocks = []
        
        # Simple regex-based parsing for Java
        
        # Find class declarations
        class_pattern = r'(public\s+|private\s+|protected\s+)?class\s+(\w+)'
        for match in re.finditer(class_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            symbol = CodeSymbol(
                name=match.group(2),
                type="class",
                file_path=file_path,
                line_number=line_number,
                column_number=match.start() - content.rfind('\n', 0, match.start()),
                scope="global",
                signature=match.group(0),
                docstring=None,
                dependencies=[],
                usages=[],
                complexity_score=0.5,
                semantic_tags=[]
            )
            symbols.append(symbol)
        
        # Find method declarations
        method_pattern = r'(public\s+|private\s+|protected\s+)?(static\s+)?(\w+)\s+(\w+)\s*\([^)]*\)\s*\{'
        for match in re.finditer(method_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            symbol = CodeSymbol(
                name=match.group(4),
                type="method",
                file_path=file_path,
                line_number=line_number,
                column_number=match.start() - content.rfind('\n', 0, match.start()),
                scope="class",
                signature=match.group(0),
                docstring=None,
                dependencies=[],
                usages=[],
                complexity_score=0.5,
                semantic_tags=[]
            )
            symbols.append(symbol)
        
        return symbols, blocks

    def _parse_cpp_file(self, content: str, file_path: str) -> Tuple[List[CodeSymbol], List[CodeBlock]]:
        """Parse a C++ file."""
        symbols = []
        blocks = []
        
        # Simple regex-based parsing for C++
        
        # Find function declarations
        function_pattern = r'(\w+)\s+(\w+)\s*\([^)]*\)\s*\{'
        for match in re.finditer(function_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            symbol = CodeSymbol(
                name=match.group(2),
                type="function",
                file_path=file_path,
                line_number=line_number,
                column_number=match.start() - content.rfind('\n', 0, match.start()),
                scope="global",
                signature=match.group(0),
                docstring=None,
                dependencies=[],
                usages=[],
                complexity_score=0.5,
                semantic_tags=[]
            )
            symbols.append(symbol)
        
        # Find class declarations
        class_pattern = r'class\s+(\w+)'
        for match in re.finditer(class_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            symbol = CodeSymbol(
                name=match.group(1),
                type="class",
                file_path=file_path,
                line_number=line_number,
                column_number=match.start() - content.rfind('\n', 0, match.start()),
                scope="global",
                signature=match.group(0),
                docstring=None,
                dependencies=[],
                usages=[],
                complexity_score=0.5,
                semantic_tags=[]
            )
            symbols.append(symbol)
        
        return symbols, blocks

    def _parse_c_file(self, content: str, file_path: str) -> Tuple[List[CodeSymbol], List[CodeBlock]]:
        """Parse a C file."""
        # Use C++ parser as base
        return self._parse_cpp_file(content, file_path)

    def _parse_go_file(self, content: str, file_path: str) -> Tuple[List[CodeSymbol], List[CodeBlock]]:
        """Parse a Go file."""
        symbols = []
        blocks = []
        
        # Simple regex-based parsing for Go
        
        # Find function declarations
        function_pattern = r'func\s+(\w+)\s*\([^)]*\)'
        for match in re.finditer(function_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            symbol = CodeSymbol(
                name=match.group(1),
                type="function",
                file_path=file_path,
                line_number=line_number,
                column_number=match.start() - content.rfind('\n', 0, match.start()),
                scope="global",
                signature=match.group(0),
                docstring=None,
                dependencies=[],
                usages=[],
                complexity_score=0.5,
                semantic_tags=[]
            )
            symbols.append(symbol)
        
        # Find struct declarations
        struct_pattern = r'type\s+(\w+)\s+struct'
        for match in re.finditer(struct_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            symbol = CodeSymbol(
                name=match.group(1),
                type="struct",
                file_path=file_path,
                line_number=line_number,
                column_number=match.start() - content.rfind('\n', 0, match.start()),
                scope="global",
                signature=match.group(0),
                docstring=None,
                dependencies=[],
                usages=[],
                complexity_score=0.5,
                semantic_tags=[]
            )
            symbols.append(symbol)
        
        return symbols, blocks

    def _parse_rust_file(self, content: str, file_path: str) -> Tuple[List[CodeSymbol], List[CodeBlock]]:
        """Parse a Rust file."""
        symbols = []
        blocks = []
        
        # Simple regex-based parsing for Rust
        
        # Find function declarations
        function_pattern = r'fn\s+(\w+)\s*\([^)]*\)'
        for match in re.finditer(function_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            symbol = CodeSymbol(
                name=match.group(1),
                type="function",
                file_path=file_path,
                line_number=line_number,
                column_number=match.start() - content.rfind('\n', 0, match.start()),
                scope="global",
                signature=match.group(0),
                docstring=None,
                dependencies=[],
                usages=[],
                complexity_score=0.5,
                semantic_tags=[]
            )
            symbols.append(symbol)
        
        # Find struct declarations
        struct_pattern = r'struct\s+(\w+)'
        for match in re.finditer(struct_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            symbol = CodeSymbol(
                name=match.group(1),
                type="struct",
                file_path=file_path,
                line_number=line_number,
                column_number=match.start() - content.rfind('\n', 0, match.start()),
                scope="global",
                signature=match.group(0),
                docstring=None,
                dependencies=[],
                usages=[],
                complexity_score=0.5,
                semantic_tags=[]
            )
            symbols.append(symbol)
        
        return symbols, blocks

    def _search_symbols(self, query: str, context: Optional[str]) -> List[SearchResult]:
        """Search symbols based on query."""
        results = []
        query_lower = query.lower()
        
        for symbol in self.symbols.values():
            relevance_score = 0.0
            reasoning = []
            
            # Name matching
            if query_lower in symbol.name.lower():
                relevance_score += 0.8
                reasoning.append("Name contains query")
            
            # Type matching
            if query_lower == symbol.type.lower():
                relevance_score += 0.6
                reasoning.append("Type matches query")
            
            # Docstring matching
            if symbol.docstring and query_lower in symbol.docstring.lower():
                relevance_score += 0.4
                reasoning.append("Docstring contains query")
            
            # Semantic tags matching
            for tag in symbol.semantic_tags:
                if query_lower in tag.lower():
                    relevance_score += 0.3
                    reasoning.append("Semantic tag matches")
            
            if relevance_score > 0:
                results.append(SearchResult(
                    symbol=symbol,
                    block=None,
                    relevance_score=relevance_score,
                    context_match=f"{symbol.type} {symbol.name}",
                    reasoning=reasoning
                ))
        
        return results

    def _search_blocks(self, query: str, context: Optional[str]) -> List[SearchResult]:
        """Search code blocks based on query."""
        results = []
        query_lower = query.lower()
        
        for block in self.blocks.values():
            relevance_score = 0.0
            reasoning = []
            
            # Content matching
            if query_lower in block.content.lower():
                relevance_score += 0.5
                reasoning.append("Block content contains query")
            
            # Intent tags matching
            for tag in block.intent_tags:
                if query_lower in tag.lower():
                    relevance_score += 0.4
                    reasoning.append("Intent tag matches")
            
            if relevance_score > 0:
                results.append(SearchResult(
                    symbol=None,
                    block=block,
                    relevance_score=relevance_score,
                    context_match=f"{block.block_type} block",
                    reasoning=reasoning
                ))
        
        return results

    def _build_dependency_graph(self):
        """Build the dependency graph."""
        # This would analyze imports, function calls, etc.
        # For now, just a placeholder
        pass

    def _create_semantic_clusters(self):
        """Create semantic clusters of related code."""
        # This would use embeddings to cluster related code
        # For now, just a placeholder
        pass

    # Helper methods
    def _get_scope(self, node, tree) -> str:
        """Get the scope of a node."""
        # Simple scope detection
        return "global"

    def _get_function_signature(self, node) -> str:
        """Get function signature."""
        if hasattr(node, 'args'):
            args = [arg.arg for arg in node.args.args]
            return f"{node.name}({', '.join(args)})"
        return node.name

    def _extract_dependencies(self, node) -> List[str]:
        """Extract dependencies from a node."""
        # This would analyze the AST for dependencies
        return []

    def _calculate_complexity(self, node) -> float:
        """Calculate complexity score for a node."""
        # Simple complexity calculation
        return 0.5

    def _extract_semantic_tags(self, node, content: str) -> List[str]:
        """Extract semantic tags from a node."""
        # This would extract meaningful tags
        return []

    def _find_dependency_chain(self, symbol_name: str) -> List[str]:
        """Find dependency chain for a symbol."""
        # This would trace the dependency chain
        return []

    def search(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """Search code with a simple interface.

        Args:
            query: The search query
            max_results: Maximum number of results

        Returns:
            List of search results as dictionaries
        """
        try:
            # Use semantic search
            results = self.search_semantic(query, max_results=max_results)

            # Convert to simple dictionary format
            return [
                {
                    "file_path": result.symbol.file_path if result.symbol else (result.block.file_path if result.block else "unknown"),
                    "line_number": result.symbol.line_number if result.symbol else (result.block.start_line if result.block else 0),
                    "content": result.context_match,
                    "relevance_score": result.relevance_score,
                    "symbol_name": result.symbol.name if result.symbol else "unknown",
                    "symbol_type": result.symbol.type if result.symbol else (result.block.block_type if result.block else "unknown")
                }
                for result in results
            ]
        except Exception as e:
            logger.error(f"Error in search: {e}")
            return []

    def index_directory(self, directory: Path):
        """Index a specific directory.

        Args:
            directory: The directory to index
        """
        try:
            # Update workspace directory temporarily
            original_workspace = self.workspace_dir
            self.workspace_dir = directory

            # Index the codebase
            self.index_codebase(force_reindex=True)

            # Restore original workspace
            self.workspace_dir = original_workspace

            logger.info(f"Successfully indexed directory: {directory}")
        except Exception as e:
            logger.error(f"Error indexing directory {directory}: {e}")

    def _find_circular_dependencies(self, symbol_name: str) -> List[str]:
        """Find circular dependencies."""
        # This would detect circular dependencies
        return []
