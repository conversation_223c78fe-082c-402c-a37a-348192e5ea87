"""
Performance Analyzer for the AI Code Assistant.

This module provides performance bottleneck identification and analysis
capabilities for code optimization.
"""

import time
import logging
import threading
import ast
import re
from typing import Dict, List, Optional, Any, Union, Tuple, Callable
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class PerformanceBottleneck:
    """Represents a performance bottleneck."""
    bottleneck_type: str
    severity: str  # critical, high, medium, low
    description: str
    location: Dict[str, int]  # line, column
    impact_score: float  # 0.0 to 1.0
    estimated_improvement: str  # percentage or description
    optimization_suggestions: List[str]
    complexity_analysis: Dict[str, Any]
    confidence: float

@dataclass
class PerformanceMetrics:
    """Performance metrics for code analysis."""
    time_complexity: str
    space_complexity: str
    cyclomatic_complexity: int
    cognitive_complexity: int
    loop_depth: int
    function_count: int
    class_count: int
    line_count: int
    performance_score: float

@dataclass
class PerformanceAnalysis:
    """Result of performance analysis."""
    bottlenecks: List[PerformanceBottleneck]
    metrics: PerformanceMetrics
    optimization_opportunities: List[str]
    performance_rating: str  # excellent, good, fair, poor
    analysis_time: float
    recommendations: List[str]

class PerformanceAnalyzer:
    """Performance analysis and bottleneck identification system."""

    def __init__(self, workspace_dir: Path, model_manager=None):
        """Initialize the performance analyzer.
        
        Args:
            workspace_dir: The workspace directory
            model_manager: Optional model manager for AI-assisted analysis
        """
        self.workspace_dir = workspace_dir
        self.model_manager = model_manager
        self.performance_patterns: Dict[str, Any] = {}
        self.analysis_history: List[PerformanceAnalysis] = []
        self.benchmarks: Dict[str, float] = {}
        self.lock = threading.RLock()

        # Monitor callbacks
        self.monitor_callbacks: List[Callable[[Dict[str, Any]], None]] = []

        # Initialize performance patterns
        self._initialize_performance_patterns()

    def _initialize_performance_patterns(self):
        """Initialize performance analysis patterns."""
        self.performance_patterns = {
            "nested_loops": {
                "complexity": "O(n²) or higher",
                "severity": "high",
                "impact": 0.8,
                "suggestions": [
                    "Consider using more efficient algorithms",
                    "Use data structures like sets or dictionaries for lookups",
                    "Break early when possible"
                ]
            },
            "inefficient_search": {
                "complexity": "O(n) linear search",
                "severity": "medium",
                "impact": 0.6,
                "suggestions": [
                    "Use binary search for sorted data",
                    "Use hash tables for constant-time lookups",
                    "Index data structures appropriately"
                ]
            },
            "string_concatenation": {
                "complexity": "O(n²) for repeated concatenation",
                "severity": "medium",
                "impact": 0.5,
                "suggestions": [
                    "Use join() for multiple string concatenations",
                    "Use f-strings or format() for string formatting",
                    "Consider using StringIO for large text building"
                ]
            },
            "recursive_without_memoization": {
                "complexity": "Exponential time",
                "severity": "critical",
                "impact": 0.9,
                "suggestions": [
                    "Add memoization to cache results",
                    "Convert to iterative approach",
                    "Use dynamic programming"
                ]
            },
            "inefficient_data_structure": {
                "complexity": "Suboptimal operations",
                "severity": "medium",
                "impact": 0.6,
                "suggestions": [
                    "Choose appropriate data structures",
                    "Use collections.deque for queue operations",
                    "Use sets for membership testing"
                ]
            }
        }

    def analyze_performance(self, code: str, language: str = "python") -> PerformanceAnalysis:
        """Analyze code performance and identify bottlenecks.
        
        Args:
            code: The code to analyze
            language: Programming language
            
        Returns:
            Performance analysis result
        """
        with self.lock:
            start_time = time.time()
            logger.info(f"Analyzing performance of {language} code")
            
            bottlenecks = []
            
            if language == "python":
                bottlenecks.extend(self._analyze_python_performance(code))
            elif language in ["javascript", "typescript"]:
                bottlenecks.extend(self._analyze_javascript_performance(code))
            else:
                bottlenecks.extend(self._analyze_generic_performance(code))
            
            # Calculate performance metrics
            metrics = self._calculate_performance_metrics(code, language)
            
            # Identify optimization opportunities
            optimization_opportunities = self._identify_optimization_opportunities(bottlenecks, metrics)
            
            # Calculate performance rating
            performance_rating = self._calculate_performance_rating(bottlenecks, metrics)
            
            # Generate recommendations
            recommendations = self._generate_performance_recommendations(bottlenecks, metrics)
            
            analysis_time = time.time() - start_time
            
            analysis = PerformanceAnalysis(
                bottlenecks=bottlenecks,
                metrics=metrics,
                optimization_opportunities=optimization_opportunities,
                performance_rating=performance_rating,
                analysis_time=analysis_time,
                recommendations=recommendations
            )
            
            # Store in history
            self.analysis_history.append(analysis)
            
            logger.info(f"Found {len(bottlenecks)} performance bottlenecks in {analysis_time:.3f}s")
            return analysis

    def _analyze_python_performance(self, code: str) -> List[PerformanceBottleneck]:
        """Analyze Python code for performance bottlenecks."""
        bottlenecks = []
        
        try:
            tree = ast.parse(code)
            
            # Analyze nested loops
            bottlenecks.extend(self._detect_nested_loops(tree))
            
            # Analyze inefficient operations
            bottlenecks.extend(self._detect_inefficient_operations(tree, code))
            
            # Analyze recursive functions
            bottlenecks.extend(self._detect_inefficient_recursion(tree))
            
            # Analyze data structure usage
            bottlenecks.extend(self._detect_inefficient_data_structures(tree, code))
            
        except SyntaxError:
            logger.warning("Syntax error in code, skipping AST analysis")
        
        return bottlenecks

    def _detect_nested_loops(self, tree: ast.AST) -> List[PerformanceBottleneck]:
        """Detect nested loops that may cause performance issues."""
        bottlenecks = []
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.For, ast.While)):
                # Check for nested loops
                nested_loops = []
                for child in ast.walk(node):
                    if isinstance(child, (ast.For, ast.While)) and child != node:
                        nested_loops.append(child)
                
                if nested_loops:
                    depth = self._calculate_loop_depth(node)
                    severity = "critical" if depth > 2 else "high"
                    
                    bottleneck = PerformanceBottleneck(
                        bottleneck_type="nested_loops",
                        severity=severity,
                        description=f"Nested loops with depth {depth} detected",
                        location={"line": node.lineno, "column": node.col_offset},
                        impact_score=min(0.3 * depth, 1.0),
                        estimated_improvement="50-90% performance improvement possible",
                        optimization_suggestions=self.performance_patterns["nested_loops"]["suggestions"],
                        complexity_analysis={"time_complexity": f"O(n^{depth})", "depth": depth},
                        confidence=0.9
                    )
                    bottlenecks.append(bottleneck)
        
        return bottlenecks

    def _detect_inefficient_operations(self, tree: ast.AST, code: str) -> List[PerformanceBottleneck]:
        """Detect inefficient operations in the code."""
        bottlenecks = []
        lines = code.splitlines()
        
        for node in ast.walk(tree):
            # Detect string concatenation in loops
            if isinstance(node, (ast.For, ast.While)):
                for child in ast.walk(node):
                    if isinstance(child, ast.AugAssign) and isinstance(child.op, ast.Add):
                        if isinstance(child.target, ast.Name):
                            # Check if it's string concatenation
                            line_content = lines[child.lineno - 1] if child.lineno <= len(lines) else ""
                            if "+=" in line_content and ("str" in line_content or '"' in line_content or "'" in line_content):
                                bottleneck = PerformanceBottleneck(
                                    bottleneck_type="string_concatenation",
                                    severity="medium",
                                    description="String concatenation in loop detected",
                                    location={"line": child.lineno, "column": child.col_offset},
                                    impact_score=0.6,
                                    estimated_improvement="30-70% performance improvement",
                                    optimization_suggestions=self.performance_patterns["string_concatenation"]["suggestions"],
                                    complexity_analysis={"time_complexity": "O(n²)", "operation": "string_concat"},
                                    confidence=0.8
                                )
                                bottlenecks.append(bottleneck)
            
            # Detect inefficient membership testing
            elif isinstance(node, ast.Compare):
                for op in node.ops:
                    if isinstance(op, ast.In):
                        # Check if testing membership in a list
                        if isinstance(node.comparators[0], ast.List):
                            bottleneck = PerformanceBottleneck(
                                bottleneck_type="inefficient_search",
                                severity="medium",
                                description="Membership testing in list instead of set",
                                location={"line": node.lineno, "column": node.col_offset},
                                impact_score=0.5,
                                estimated_improvement="90% performance improvement for large lists",
                                optimization_suggestions=["Convert list to set for membership testing"],
                                complexity_analysis={"time_complexity": "O(n) vs O(1)", "operation": "membership_test"},
                                confidence=0.9
                            )
                            bottlenecks.append(bottleneck)
        
        return bottlenecks

    def _detect_inefficient_recursion(self, tree: ast.AST) -> List[PerformanceBottleneck]:
        """Detect inefficient recursive functions."""
        bottlenecks = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # Check if function calls itself
                function_name = node.name
                has_recursive_call = False
                has_memoization = False
                
                for child in ast.walk(node):
                    if isinstance(child, ast.Call) and isinstance(child.func, ast.Name):
                        if child.func.id == function_name:
                            has_recursive_call = True
                    
                    # Simple check for memoization patterns
                    if isinstance(child, ast.Name) and child.id in ["cache", "memo", "lru_cache"]:
                        has_memoization = True
                
                if has_recursive_call and not has_memoization:
                    # Check if it's a potentially expensive recursive function
                    if self._is_potentially_expensive_recursion(node):
                        bottleneck = PerformanceBottleneck(
                            bottleneck_type="recursive_without_memoization",
                            severity="critical",
                            description=f"Recursive function '{function_name}' without memoization",
                            location={"line": node.lineno, "column": node.col_offset},
                            impact_score=0.9,
                            estimated_improvement="Exponential to polynomial time improvement",
                            optimization_suggestions=self.performance_patterns["recursive_without_memoization"]["suggestions"],
                            complexity_analysis={"time_complexity": "Exponential", "function": function_name},
                            confidence=0.8
                        )
                        bottlenecks.append(bottleneck)
        
        return bottlenecks

    def _detect_inefficient_data_structures(self, tree: ast.AST, code: str) -> List[PerformanceBottleneck]:
        """Detect inefficient data structure usage."""
        bottlenecks = []
        
        # This is a simplified analysis - in practice, would be more sophisticated
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Attribute):
                    # Check for inefficient list operations
                    if node.func.attr == "insert" and isinstance(node.func.value, ast.Name):
                        # insert(0, ...) is O(n) for lists
                        if (node.args and isinstance(node.args[0], ast.Constant) and 
                            node.args[0].value == 0):
                            bottleneck = PerformanceBottleneck(
                                bottleneck_type="inefficient_data_structure",
                                severity="medium",
                                description="Using list.insert(0, ...) which is O(n)",
                                location={"line": node.lineno, "column": node.col_offset},
                                impact_score=0.6,
                                estimated_improvement="Use collections.deque for O(1) prepend",
                                optimization_suggestions=["Use collections.deque for frequent prepend operations"],
                                complexity_analysis={"time_complexity": "O(n) vs O(1)", "operation": "prepend"},
                                confidence=0.9
                            )
                            bottlenecks.append(bottleneck)
        
        return bottlenecks

    def _analyze_javascript_performance(self, code: str) -> List[PerformanceBottleneck]:
        """Analyze JavaScript code for performance bottlenecks."""
        bottlenecks = []
        lines = code.splitlines()
        
        for line_num, line in enumerate(lines, 1):
            # Check for inefficient DOM operations
            if re.search(r'document\.getElementById|document\.querySelector', line):
                if "for" in line or "while" in line:
                    bottleneck = PerformanceBottleneck(
                        bottleneck_type="dom_operations_in_loop",
                        severity="high",
                        description="DOM operations inside loop",
                        location={"line": line_num, "column": 0},
                        impact_score=0.8,
                        estimated_improvement="Cache DOM elements outside loop",
                        optimization_suggestions=["Cache DOM elements", "Use document fragments"],
                        complexity_analysis={"operation": "dom_access"},
                        confidence=0.7
                    )
                    bottlenecks.append(bottleneck)
            
            # Check for inefficient array operations
            if re.search(r'\.concat\(', line) and ("for" in line or "while" in line):
                bottleneck = PerformanceBottleneck(
                    bottleneck_type="array_concat_in_loop",
                    severity="medium",
                    description="Array concatenation in loop",
                    location={"line": line_num, "column": line.find('.concat(')},
                    impact_score=0.6,
                    estimated_improvement="Use push() or spread operator",
                    optimization_suggestions=["Use Array.push()", "Use spread operator"],
                    complexity_analysis={"operation": "array_concat"},
                    confidence=0.8
                )
                bottlenecks.append(bottleneck)
        
        return bottlenecks

    def _analyze_generic_performance(self, code: str) -> List[PerformanceBottleneck]:
        """Analyze generic performance issues."""
        bottlenecks = []
        lines = code.splitlines()
        
        # Count nested loops (simple heuristic)
        loop_depth = 0
        max_depth = 0
        
        for line_num, line in enumerate(lines, 1):
            stripped = line.strip()
            if any(keyword in stripped for keyword in ["for", "while"]):
                loop_depth += 1
                max_depth = max(max_depth, loop_depth)
            elif stripped.startswith("}") or (stripped == "" and loop_depth > 0):
                loop_depth = max(0, loop_depth - 1)
        
        if max_depth > 2:
            bottleneck = PerformanceBottleneck(
                bottleneck_type="nested_loops",
                severity="high",
                description=f"Deep nested loops detected (depth: {max_depth})",
                location={"line": 1, "column": 0},
                impact_score=min(0.3 * max_depth, 1.0),
                estimated_improvement="Consider algorithm optimization",
                optimization_suggestions=["Review algorithm complexity", "Consider alternative approaches"],
                complexity_analysis={"estimated_depth": max_depth},
                confidence=0.6
            )
            bottlenecks.append(bottleneck)
        
        return bottlenecks

    def _calculate_performance_metrics(self, code: str, language: str) -> PerformanceMetrics:
        """Calculate performance metrics for the code."""
        lines = code.splitlines()
        line_count = len([line for line in lines if line.strip()])
        
        # Basic metrics calculation
        function_count = 0
        class_count = 0
        loop_depth = 0
        cyclomatic_complexity = 1  # Base complexity
        
        if language == "python":
            try:
                tree = ast.parse(code)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        function_count += 1
                    elif isinstance(node, ast.ClassDef):
                        class_count += 1
                    elif isinstance(node, (ast.If, ast.For, ast.While, ast.Try)):
                        cyclomatic_complexity += 1
                
                loop_depth = self._calculate_max_loop_depth(tree)
                
            except SyntaxError:
                pass
        else:
            # Generic counting for other languages
            for line in lines:
                if any(keyword in line for keyword in ["function", "def", "func"]):
                    function_count += 1
                elif any(keyword in line for keyword in ["class", "struct"]):
                    class_count += 1
                elif any(keyword in line for keyword in ["if", "for", "while"]):
                    cyclomatic_complexity += 1
        
        # Calculate performance score (0-1, higher is better)
        performance_score = self._calculate_performance_score(
            line_count, function_count, cyclomatic_complexity, loop_depth
        )
        
        return PerformanceMetrics(
            time_complexity="O(n)",  # Default estimate
            space_complexity="O(1)",  # Default estimate
            cyclomatic_complexity=cyclomatic_complexity,
            cognitive_complexity=cyclomatic_complexity,  # Simplified
            loop_depth=loop_depth,
            function_count=function_count,
            class_count=class_count,
            line_count=line_count,
            performance_score=performance_score
        )

    def _calculate_loop_depth(self, node: ast.AST) -> int:
        """Calculate the depth of nested loops."""
        max_depth = 0
        current_depth = 0
        
        def visit_node(n, depth):
            nonlocal max_depth
            if isinstance(n, (ast.For, ast.While)):
                depth += 1
                max_depth = max(max_depth, depth)
            
            for child in ast.iter_child_nodes(n):
                visit_node(child, depth)
        
        visit_node(node, current_depth)
        return max_depth

    def _calculate_max_loop_depth(self, tree: ast.AST) -> int:
        """Calculate maximum loop depth in the entire tree."""
        max_depth = 0
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.For, ast.While)):
                depth = self._calculate_loop_depth(node)
                max_depth = max(max_depth, depth)
        
        return max_depth

    def _is_potentially_expensive_recursion(self, node: ast.FunctionDef) -> bool:
        """Check if a recursive function is potentially expensive."""
        # Simple heuristic: multiple recursive calls suggest exponential complexity
        recursive_calls = 0
        function_name = node.name
        
        for child in ast.walk(node):
            if isinstance(child, ast.Call) and isinstance(child.func, ast.Name):
                if child.func.id == function_name:
                    recursive_calls += 1
        
        return recursive_calls > 1

    def _calculate_performance_score(self, line_count: int, function_count: int, 
                                   complexity: int, loop_depth: int) -> float:
        """Calculate overall performance score."""
        # Normalize factors
        line_factor = max(0, 1 - (line_count / 1000))  # Penalize very long code
        complexity_factor = max(0, 1 - (complexity / 50))  # Penalize high complexity
        depth_factor = max(0, 1 - (loop_depth / 5))  # Penalize deep nesting
        
        # Weighted average
        score = (line_factor * 0.2 + complexity_factor * 0.5 + depth_factor * 0.3)
        return max(0.0, min(1.0, score))

    def _identify_optimization_opportunities(self, bottlenecks: List[PerformanceBottleneck], 
                                           metrics: PerformanceMetrics) -> List[str]:
        """Identify optimization opportunities."""
        opportunities = []
        
        # Based on bottlenecks
        bottleneck_types = {b.bottleneck_type for b in bottlenecks}
        
        if "nested_loops" in bottleneck_types:
            opportunities.append("Algorithm optimization for nested loops")
        if "string_concatenation" in bottleneck_types:
            opportunities.append("String handling optimization")
        if "inefficient_search" in bottleneck_types:
            opportunities.append("Data structure optimization")
        
        # Based on metrics
        if metrics.cyclomatic_complexity > 20:
            opportunities.append("Code complexity reduction")
        if metrics.loop_depth > 3:
            opportunities.append("Loop structure simplification")
        if metrics.performance_score < 0.5:
            opportunities.append("General performance optimization")
        
        return opportunities

    def _calculate_performance_rating(self, bottlenecks: List[PerformanceBottleneck], 
                                    metrics: PerformanceMetrics) -> str:
        """Calculate overall performance rating."""
        critical_bottlenecks = len([b for b in bottlenecks if b.severity == "critical"])
        high_bottlenecks = len([b for b in bottlenecks if b.severity == "high"])
        
        if critical_bottlenecks > 0 or metrics.performance_score < 0.3:
            return "poor"
        elif high_bottlenecks > 2 or metrics.performance_score < 0.6:
            return "fair"
        elif high_bottlenecks > 0 or metrics.performance_score < 0.8:
            return "good"
        else:
            return "excellent"

    def _generate_performance_recommendations(self, bottlenecks: List[PerformanceBottleneck], 
                                            metrics: PerformanceMetrics) -> List[str]:
        """Generate performance recommendations."""
        recommendations = []
        
        # Priority recommendations based on severity
        critical_bottlenecks = [b for b in bottlenecks if b.severity == "critical"]
        if critical_bottlenecks:
            recommendations.append("Address critical performance bottlenecks immediately")
        
        # Specific recommendations
        if metrics.cyclomatic_complexity > 15:
            recommendations.append("Consider breaking down complex functions")
        
        if metrics.loop_depth > 2:
            recommendations.append("Review nested loop structures for optimization opportunities")
        
        if len(bottlenecks) > 5:
            recommendations.append("Conduct comprehensive performance review")
        
        # General recommendations
        if metrics.performance_score < 0.7:
            recommendations.extend([
                "Profile code execution to identify hotspots",
                "Consider using more efficient algorithms and data structures",
                "Add performance monitoring to track improvements"
            ])
        
        return recommendations

    def get_performance_statistics(self) -> Dict[str, Any]:
        """Get performance analysis statistics."""
        with self.lock:
            if not self.analysis_history:
                return {"total_analyses": 0}
            
            total_analyses = len(self.analysis_history)
            total_bottlenecks = sum(len(a.bottlenecks) for a in self.analysis_history)
            avg_performance_score = sum(a.metrics.performance_score for a in self.analysis_history) / total_analyses
            
            # Count bottleneck types
            bottleneck_types = {}
            for analysis in self.analysis_history:
                for bottleneck in analysis.bottlenecks:
                    bottleneck_types[bottleneck.bottleneck_type] = bottleneck_types.get(bottleneck.bottleneck_type, 0) + 1
            
            return {
                "total_analyses": total_analyses,
                "total_bottlenecks_found": total_bottlenecks,
                "average_performance_score": avg_performance_score,
                "bottleneck_types": bottleneck_types,
                "patterns_count": len(self.performance_patterns)
            }

    def add_monitor_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add a monitor callback for performance updates.

        Args:
            callback: A callable that receives performance metrics
        """
        self.monitor_callbacks.append(callback)
        logger.info("Monitor callback added to performance analyzer")

    def _notify_monitor_callbacks(self, metrics: Dict[str, Any]):
        """Notify all monitor callbacks with performance metrics."""
        for callback in self.monitor_callbacks:
            try:
                callback(metrics)
            except Exception as e:
                logger.error(f"Error in monitor callback: {e}")

    def clear_history(self):
        """Clear analysis history."""
        with self.lock:
            self.analysis_history.clear()
            logger.info("Cleared performance analysis history")
