"""
Tools package for the Advanced AI Agent.
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from tools.shell import ShellTool
from tools.file import FileTool
from tools.code import CodeTool
from tools.web import WebTool
from tools.codebase import CodebaseTool
from tools.vision import VisionTool
from tools.tmux import TmuxTool
from tools.patch import PatchTool
from tools.browser import BrowserTool
from tools.rag import RagTool
from tools.search_api import SearchAPI
from tools.web_scraper import WebScraperTool
from tools.info_synthesizer import InformationSynthesizer
from tools.web_info_manager import WebInfoManager

__all__ = [
    "ShellTool",
    "FileTool",
    "CodeTool",
    "WebTool",
    "CodebaseTool",
    "VisionTool",
    "TmuxTool",
    "PatchTool",
    "BrowserTool",
    "RagTool",
    "SearchAPI",
    "WebScraperTool",
    "InformationSynthesizer",
    "WebInfoManager",
]
