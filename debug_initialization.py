#!/usr/bin/env python3
"""
Debug script to isolate initialization issues.
"""

import sys
import os
from pathlib import Path
import traceback

# Add the current directory to the path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_step_by_step():
    """Test each component step by step."""
    print("=== Step-by-step Initialization Debug ===")
    
    try:
        print("1. Testing basic imports...")
        from models import ModelManager
        from conversation import ConversationManager
        print("✓ Basic imports successful")
        
        print("\n2. Testing ModelManager creation...")
        model_manager = ModelManager(
            provider="gemini",
            model_name="gemini-2.0-flash",
            temperature=0.7,
            max_tokens=4096
        )
        print("✓ ModelManager created successfully")
        
        print("\n3. Testing ConversationManager creation...")
        workspace_dir = Path.cwd()
        conversation_manager = ConversationManager(workspace_dir / "conversations")
        print("✓ ConversationManager created successfully")
        
        print("\n4. Testing AdvancedMasterController import...")
        from core.advanced_master_controller import AdvancedMasterController
        print("✓ AdvancedMasterController imported successfully")
        
        print("\n5. Testing AdvancedMasterController creation...")
        try:
            master_controller = AdvancedMasterController(model_manager, workspace_dir)
            print("✓ AdvancedMasterController created successfully")
            return True
        except Exception as e:
            print(f"✗ AdvancedMasterController creation failed: {e}")
            print("Full traceback:")
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"✗ Error in step-by-step test: {e}")
        print("Full traceback:")
        traceback.print_exc()
        return False

def test_individual_subsystems():
    """Test individual subsystems."""
    print("\n=== Individual Subsystem Tests ===")
    
    try:
        from models import ModelManager
        workspace_dir = Path.cwd()
        model_manager = ModelManager(
            provider="gemini",
            model_name="gemini-2.0-flash",
            temperature=0.7,
            max_tokens=4096
        )
        
        subsystems = [
            ("SelfAnalyzingIntelligence", "core.self_analyzing_intelligence", "SelfAnalyzingIntelligence"),
            ("AICodeAssistant", "core.ai_code_assistant", "AICodeAssistant"),
            ("RAGEnhancedSystem", "core.rag_enhanced_system", "RAGEnhancedSystem"),
            ("ContextAwareCompletion", "core.context_aware_completion", "ContextAwareCompletion"),
            ("IntelligentRefactoring", "core.intelligent_refactoring", "IntelligentRefactoring"),
            ("PredictiveDebugger", "core.predictive_debugger", "PredictiveDebugger"),
            ("PerformanceAnalyzer", "core.performance_analyzer", "PerformanceAnalyzer"),
            ("OptimizationEngine", "core.optimization_engine", "OptimizationEngine"),
            ("MultiLanguageProcessor", "core.multi_language_processor", "MultiLanguageProcessor"),
            ("SemanticIndexer", "core.semantic_indexer", "SemanticIndexer"),
            ("LearningSystem", "core.learning_system", "LearningSystem"),
        ]
        
        for name, module_path, class_name in subsystems:
            try:
                print(f"Testing {name}...")
                module = __import__(module_path, fromlist=[class_name])
                cls = getattr(module, class_name)
                
                # Test initialization with appropriate parameters
                if name == "MultiLanguageProcessor":
                    instance = cls(workspace_dir)
                elif name == "SemanticIndexer":
                    instance = cls(workspace_dir, model_manager)
                elif name == "LearningSystem":
                    instance = cls(workspace_dir, model_manager)
                else:
                    instance = cls(model_manager, workspace_dir)
                
                print(f"✓ {name} created successfully")
                
            except Exception as e:
                print(f"✗ {name} failed: {e}")
                print("Traceback:")
                traceback.print_exc()
                print()
        
        return True
        
    except Exception as e:
        print(f"✗ Error in subsystem tests: {e}")
        traceback.print_exc()
        return False

def main():
    """Main debug function."""
    print("Enhanced AI Coding Assistant - Initialization Debug")
    print("=" * 60)
    
    success1 = test_step_by_step()
    success2 = test_individual_subsystems()
    
    if success1 and success2:
        print("\n🎉 All initialization tests passed!")
        return True
    else:
        print("\n❌ Some initialization tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
