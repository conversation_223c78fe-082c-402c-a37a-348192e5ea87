#!/usr/bin/env python3
"""
Demonstration script for the Autonomous AI Agent System.

This script showcases the key capabilities of the autonomous agent:
1. Autonomous task execution from start to completion
2. Comprehensive research and information gathering
3. Multi-step workflow planning and execution
4. Real-time progress monitoring and reporting
5. Error handling and recovery mechanisms
"""

import sys
import os
import time
import asyncio
from pathlib import Path
from typing import Dict, Any

# Add the current directory to the path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from models import ModelManager
from conversation import ConversationManager
from agent import Agent
from config import load_config
from utils import get_logger

logger = get_logger()

class AutonomousAgentDemo:
    """Demonstration of autonomous agent capabilities."""
    
    def __init__(self):
        """Initialize the demo."""
        print("🤖 Autonomous AI Agent System Demo")
        print("=" * 50)
        
        # Load configuration
        self.config = load_config()
        self.workspace_dir = Path(self.config.workspace_dir)
        
        # Initialize components
        print("🔧 Initializing AI Agent Components...")
        
        self.model_manager = ModelManager(
            provider=self.config.agent.provider,
            model_name=self.config.agent.model,
            temperature=self.config.agent.temperature,
            max_tokens=self.config.agent.max_tokens
        )
        
        self.conversation_manager = ConversationManager(self.config.history_dir)
        
        self.agent = Agent(
            model_manager=self.model_manager,
            conversation_manager=self.conversation_manager,
            workspace_dir=self.workspace_dir
        )
        
        print(f"✅ Agent initialized with {self.config.agent.provider}/{self.config.agent.model}")
        print(f"📁 Workspace: {self.workspace_dir}")
        print()
        
        # Check autonomous capabilities
        self.check_autonomous_capabilities()
    
    def check_autonomous_capabilities(self):
        """Check if autonomous capabilities are available."""
        status = self.agent.get_autonomous_status()
        
        if status.get('available', False):
            print("✅ Autonomous capabilities available")
        else:
            print("❌ Autonomous capabilities not available")
            print("   Please ensure all dependencies are installed")
            sys.exit(1)
    
    def run_demo(self):
        """Run the complete demonstration."""
        print("🚀 Starting Autonomous Agent Demonstration")
        print("=" * 50)
        
        try:
            # Demo 1: Simple autonomous task
            self.demo_simple_task()
            
            # Demo 2: Research-intensive task
            self.demo_research_task()
            
            # Demo 3: Complex multi-step workflow
            self.demo_complex_workflow()
            
            # Demo 4: Semi-autonomous mode
            self.demo_semi_autonomous()
            
            # Demo 5: Progress monitoring and control
            self.demo_progress_monitoring()
            
            print("🎉 Demonstration completed successfully!")
            
        except KeyboardInterrupt:
            print("\n⚠️ Demo interrupted by user")
            self.cleanup()
        except Exception as e:
            print(f"❌ Demo failed: {e}")
            logger.error(f"Demo failed: {e}")
            self.cleanup()
    
    def demo_simple_task(self):
        """Demonstrate simple autonomous task execution."""
        print("📋 Demo 1: Simple Autonomous Task")
        print("-" * 40)
        
        task = "Create a Python function that generates the Fibonacci sequence up to n terms"
        
        print(f"🎯 Task: {task}")
        print("🤖 Starting autonomous execution...")
        
        # Set up progress monitoring
        self.setup_progress_monitoring("Simple Task")
        
        # Start autonomous session
        session_id = self.agent.start_autonomous_mode(task, "autonomous")
        
        if session_id:
            print(f"✅ Session started: {session_id}")
            
            # Monitor execution
            self.monitor_execution(timeout=60)  # 1 minute timeout
            
            # Stop session
            self.agent.stop_autonomous_mode()
            print("🏁 Simple task demonstration completed")
        else:
            print("❌ Failed to start autonomous session")
        
        print()
        self.wait_for_user()
    
    def demo_research_task(self):
        """Demonstrate research-intensive autonomous task."""
        print("📋 Demo 2: Research-Intensive Task")
        print("-" * 40)
        
        task = "Research the latest trends in artificial intelligence and machine learning, then create a summary report"
        
        print(f"🎯 Task: {task}")
        print("🔍 This will demonstrate autonomous research capabilities...")
        
        # Set up progress monitoring
        self.setup_progress_monitoring("Research Task")
        
        # Start autonomous session
        session_id = self.agent.start_autonomous_mode(task, "autonomous")
        
        if session_id:
            print(f"✅ Research session started: {session_id}")
            
            # Monitor with longer timeout for research
            self.monitor_execution(timeout=120)  # 2 minutes timeout
            
            # Show research history
            self.show_research_history()
            
            # Stop session
            self.agent.stop_autonomous_mode()
            print("🏁 Research task demonstration completed")
        else:
            print("❌ Failed to start research session")
        
        print()
        self.wait_for_user()
    
    def demo_complex_workflow(self):
        """Demonstrate complex multi-step workflow."""
        print("📋 Demo 3: Complex Multi-Step Workflow")
        print("-" * 40)
        
        task = "Design and implement a simple REST API for a todo application with user authentication"
        
        print(f"🎯 Task: {task}")
        print("⚙️ This will demonstrate multi-step workflow planning and execution...")
        
        # Set up progress monitoring
        self.setup_progress_monitoring("Complex Workflow")
        
        # Start autonomous session
        session_id = self.agent.start_autonomous_mode(task, "autonomous")
        
        if session_id:
            print(f"✅ Complex workflow started: {session_id}")
            
            # Monitor with extended timeout for complex task
            self.monitor_execution(timeout=180, show_steps=True)  # 3 minutes timeout
            
            # Show task breakdown
            self.show_task_breakdown()
            
            # Stop session
            self.agent.stop_autonomous_mode()
            print("🏁 Complex workflow demonstration completed")
        else:
            print("❌ Failed to start complex workflow")
        
        print()
        self.wait_for_user()
    
    def demo_semi_autonomous(self):
        """Demonstrate semi-autonomous mode with user checkpoints."""
        print("📋 Demo 4: Semi-Autonomous Mode")
        print("-" * 40)
        
        task = "Create a data visualization dashboard for sales analytics"
        
        print(f"🎯 Task: {task}")
        print("🤝 This demonstrates semi-autonomous mode with user checkpoints...")
        
        # Set up decision point monitoring
        decision_points = []
        
        def decision_callback(data):
            decision_points.append(data)
            print(f"⚠️ Decision point: {data.get('decision', 'Unknown')}")
            print(f"   Task: {data.get('task_title', 'Unknown')}")
        
        self.agent.set_autonomous_callbacks(decision_callback=decision_callback)
        
        # Start semi-autonomous session
        session_id = self.agent.start_autonomous_mode(task, "semi_autonomous")
        
        if session_id:
            print(f"✅ Semi-autonomous session started: {session_id}")
            
            # Monitor with decision point handling
            self.monitor_execution(timeout=90)  # 1.5 minutes timeout
            
            print(f"📊 Decision points encountered: {len(decision_points)}")
            
            # Stop session
            self.agent.stop_autonomous_mode()
            print("🏁 Semi-autonomous demonstration completed")
        else:
            print("❌ Failed to start semi-autonomous session")
        
        print()
        self.wait_for_user()
    
    def demo_progress_monitoring(self):
        """Demonstrate progress monitoring and control capabilities."""
        print("📋 Demo 5: Progress Monitoring & Control")
        print("-" * 40)
        
        task = "Develop a simple web scraper for news articles with data storage"
        
        print(f"🎯 Task: {task}")
        print("📊 This demonstrates progress monitoring and control features...")
        
        # Start autonomous session
        session_id = self.agent.start_autonomous_mode(task, "autonomous")
        
        if session_id:
            print(f"✅ Session started: {session_id}")
            
            # Monitor for a bit
            time.sleep(10)
            
            # Demonstrate pause
            print("⏸️ Pausing current task...")
            if self.agent.pause_autonomous_task():
                print("✅ Task paused successfully")
                time.sleep(3)
                
                # Demonstrate resume
                print("▶️ Resuming current task...")
                if self.agent.resume_autonomous_task():
                    print("✅ Task resumed successfully")
                else:
                    print("❌ Failed to resume task")
            else:
                print("❌ Failed to pause task")
            
            # Continue monitoring
            self.monitor_execution(timeout=60)
            
            # Show final status
            self.show_final_status()
            
            # Stop session
            self.agent.stop_autonomous_mode()
            print("🏁 Progress monitoring demonstration completed")
        else:
            print("❌ Failed to start monitoring session")
        
        print()
    
    def setup_progress_monitoring(self, demo_name: str):
        """Set up progress monitoring callbacks."""
        def progress_callback(data):
            task_title = data.get('task_title', 'Unknown')[:50]
            progress = data.get('progress', 0) * 100
            print(f"  📈 Progress: {task_title} - {progress:.1f}%")
        
        def status_callback(data):
            status_type = data.get('type', 'unknown')
            if status_type == 'session_started':
                print(f"  🟢 Session started: {data.get('session_id', 'Unknown')}")
            elif status_type == 'task_completed':
                print(f"  ✅ Task completed: {data.get('task_title', 'Unknown')}")
            elif status_type == 'decision_point':
                print(f"  ⚠️ Decision point: {data.get('task_title', 'Unknown')}")
        
        def completion_callback(data):
            print(f"  🎉 Task completed successfully!")
            print(f"     Final progress: {data.get('final_progress', 0)*100:.1f}%")
        
        def error_callback(error):
            print(f"  ❌ Error: {error}")
        
        self.agent.set_autonomous_callbacks(
            progress_callback=progress_callback,
            status_callback=status_callback,
            completion_callback=completion_callback,
            error_callback=error_callback
        )
    
    def monitor_execution(self, timeout: int = 60, show_steps: bool = False):
        """Monitor autonomous execution."""
        start_time = time.time()
        last_status_time = 0
        
        print(f"⏱️ Monitoring execution (timeout: {timeout}s)...")
        
        while time.time() - start_time < timeout:
            status = self.agent.get_autonomous_status()
            
            if not status.get('active', False):
                print("  🏁 Execution completed")
                break
            
            # Show periodic status updates
            if time.time() - last_status_time > 10:  # Every 10 seconds
                active_tasks = status.get('active_tasks_count', 0)
                completed_tasks = status.get('total_tasks_completed', 0)
                print(f"  📊 Status: {active_tasks} active, {completed_tasks} completed")
                
                if show_steps and status.get('active_tasks'):
                    task = status['active_tasks'][0]
                    next_steps = task.get('next_steps', [])
                    if next_steps:
                        print(f"  📋 Next: {next_steps[0][:60]}...")
                
                last_status_time = time.time()
            
            time.sleep(2)
        
        if time.time() - start_time >= timeout:
            print(f"  ⏰ Monitoring timeout reached ({timeout}s)")
    
    def show_research_history(self):
        """Show research history from autonomous execution."""
        research_history = self.agent.get_autonomous_research_history()
        
        if research_history:
            print(f"  🔍 Research conducted: {len(research_history)} queries")
            for i, research in enumerate(research_history[-3:], 1):  # Show last 3
                query = research.get('query', 'Unknown')[:50]
                confidence = research.get('confidence', 0)
                sources = research.get('sources_count', 0)
                print(f"    {i}. {query}... (Confidence: {confidence:.2f}, Sources: {sources})")
        else:
            print("  ℹ️ No research history available")
    
    def show_task_breakdown(self):
        """Show task breakdown and workflow steps."""
        status = self.agent.get_autonomous_status()
        
        if status.get('active_tasks'):
            task = status['active_tasks'][0]
            task_id = task.get('task_id')
            
            if task_id:
                details = self.agent.get_autonomous_task_details(task_id)
                if details:
                    print(f"  📋 Task breakdown:")
                    print(f"     Progress: {details.get('progress', 0)*100:.1f}%")
                    print(f"     Next steps: {len(details.get('next_steps', []))}")
                    print(f"     Checkpoints: {details.get('checkpoints', 0)}")
                    print(f"     Iterations: {details.get('iterations', 0)}")
    
    def show_final_status(self):
        """Show final execution status."""
        status = self.agent.get_autonomous_status()
        session_history = self.agent.get_autonomous_session_history()
        
        print("  📊 Final Status:")
        print(f"     Active: {status.get('active', False)}")
        print(f"     Total sessions: {len(session_history)}")
        
        if session_history:
            latest = session_history[-1]
            print(f"     Latest session tasks: {latest.get('tasks_completed', 0)}")
    
    def wait_for_user(self):
        """Wait for user input to continue."""
        try:
            input("Press Enter to continue to next demo...")
        except KeyboardInterrupt:
            raise
    
    def cleanup(self):
        """Clean up resources."""
        try:
            self.agent.stop_autonomous_mode()
            print("🧹 Cleanup completed")
        except Exception as e:
            logger.error(f"Cleanup error: {e}")

def main():
    """Main demo function."""
    try:
        demo = AutonomousAgentDemo()
        demo.run_demo()
        
    except KeyboardInterrupt:
        print("\n⚠️ Demo interrupted by user")
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        logger.error(f"Demo failed: {e}")

if __name__ == "__main__":
    main()
