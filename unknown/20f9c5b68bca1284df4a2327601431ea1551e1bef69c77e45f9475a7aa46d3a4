"""
AI Code Assistant - Main Controller.

This module integrates all core components into a cohesive self-analyzing,
intelligent code assistant with advanced capabilities.
"""

import time
import json
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

from .self_analyzer import <PERSON>Analyzer, AnalysisContext, AnalysisResult
from .execution_monitor import ExecutionMonitor, ExecutionResult
from .adaptive_controller import AdaptiveController, ContextualSituation, AdaptationDecision
from .optimization_engine import OptimizationE<PERSON>ine, OptimizationTarget, OptimizationType
from .semantic_indexer import SemanticIndexer, SearchResult
from .dependency_analyzer import DependencyAnalyzer, ImpactAnalysis
from .multi_language_processor import MultiLanguageProcessor, CodeAnalysisResult
from .rag_enhanced_generator import RAGEnhancedGenerator, GenerationResult

logger = logging.getLogger(__name__)

@dataclass
class AssistantRequest:
    """Represents a request to the AI Code Assistant."""
    request_id: str
    request_type: str  # analyze, generate, optimize, debug, refactor
    code: Optional[str]
    language: Optional[str]
    prompt: Optional[str]
    context: Dict[str, Any]
    preferences: Dict[str, Any]
    constraints: List[str]

@dataclass
class AssistantResponse:
    """Represents a response from the AI Code Assistant."""
    request_id: str
    success: bool
    result: Any
    analysis_iterations: List[AnalysisResult]
    execution_results: List[ExecutionResult]
    adaptations_made: List[AdaptationDecision]
    optimizations_applied: List[str]
    confidence_score: float
    processing_time: float
    recommendations: List[str]
    learned_insights: List[str]

class AICodeAssistant:
    """Main AI Code Assistant that integrates all core components."""

    def __init__(self, model_manager, workspace_dir: Path):
        """Initialize the AI Code Assistant.

        Args:
            model_manager: The model manager for AI interactions
            workspace_dir: The workspace directory
        """
        self.model_manager = model_manager
        self.workspace_dir = workspace_dir
        self.lock = threading.RLock()

        # Initialize core components
        logger.info("Initializing AI Code Assistant components...")

        self.self_analyzer = SelfAnalyzer(model_manager, workspace_dir)
        self.execution_monitor = ExecutionMonitor(workspace_dir)
        self.adaptive_controller = AdaptiveController(model_manager, workspace_dir)
        self.optimization_engine = OptimizationEngine(model_manager, workspace_dir)
        self.semantic_indexer = SemanticIndexer(workspace_dir, model_manager)
        self.dependency_analyzer = DependencyAnalyzer(workspace_dir, self.semantic_indexer)
        self.multi_language_processor = MultiLanguageProcessor(workspace_dir)
        self.rag_enhanced_generator = RAGEnhancedGenerator(
            model_manager, self.semantic_indexer, workspace_dir
        )

        # Assistant state
        self.request_history: List[AssistantRequest] = []
        self.response_history: List[AssistantResponse] = []
        self.learning_insights: List[str] = []
        self.performance_metrics: Dict[str, Any] = {}

        # Configuration
        self.config = {
            "max_analysis_iterations": 5,
            "analysis_timeout": 300.0,  # 5 minutes
            "enable_self_learning": True,
            "enable_adaptive_optimization": True,
            "confidence_threshold": 0.8,
        }

        logger.info("AI Code Assistant initialized successfully")

    def process_request(self, request: AssistantRequest) -> AssistantResponse:
        """Process a request using the self-analyzing loop system.

        Args:
            request: The assistant request

        Returns:
            Assistant response with comprehensive results
        """
        with self.lock:
            logger.info(f"Processing request {request.request_id}: {request.request_type}")
            start_time = time.time()

            # Store request
            self.request_history.append(request)

            # Initialize response
            response = AssistantResponse(
                request_id=request.request_id,
                success=False,
                result=None,
                analysis_iterations=[],
                execution_results=[],
                adaptations_made=[],
                optimizations_applied=[],
                confidence_score=0.0,
                processing_time=0.0,
                recommendations=[],
                learned_insights=[]
            )

            try:
                # Route request to appropriate handler
                if request.request_type == "analyze":
                    response = self._handle_analyze_request(request, response)
                elif request.request_type == "generate":
                    response = self._handle_generate_request(request, response)
                elif request.request_type == "optimize":
                    response = self._handle_optimize_request(request, response)
                elif request.request_type == "debug":
                    response = self._handle_debug_request(request, response)
                elif request.request_type == "refactor":
                    response = self._handle_refactor_request(request, response)
                else:
                    response = self._handle_general_request(request, response)

                response.success = True

            except Exception as e:
                logger.error(f"Error processing request {request.request_id}: {e}")
                response.result = {"error": str(e)}
                response.recommendations.append("Review request parameters and try again")

            # Calculate processing time
            response.processing_time = time.time() - start_time

            # Learn from the interaction
            if self.config["enable_self_learning"]:
                self._learn_from_interaction(request, response)

            # Store response
            self.response_history.append(response)

            logger.info(f"Request {request.request_id} processed in {response.processing_time:.2f}s "
                       f"with confidence {response.confidence_score:.3f}")

            return response

    def _handle_analyze_request(self, request: AssistantRequest, response: AssistantResponse) -> AssistantResponse:
        """Handle code analysis request."""
        if not request.code:
            raise ValueError("Code is required for analysis")

        # Detect language if not provided
        language = request.language or self.multi_language_processor.detect_language(request.code)

        # Create analysis context
        analysis_context = AnalysisContext(
            task_description="Code analysis and understanding",
            code=request.code,
            language=language,
            expected_outcome=request.context.get("expected_outcome"),
            constraints=request.constraints,
            optimization_goals=request.context.get("optimization_goals", []),
            max_iterations=self.config["max_analysis_iterations"],
            timeout_seconds=self.config["analysis_timeout"]
        )

        # Run self-analyzing loop
        analysis_results = self.self_analyzer.analyze_and_iterate(analysis_context)
        response.analysis_iterations = analysis_results

        # Perform multi-language analysis
        code_analysis = self.multi_language_processor.analyze_code(request.code, language)

        # Index the code for future reference
        self.semantic_indexer.index_codebase(force_reindex=False)

        # Analyze dependencies
        dependency_stats = self.dependency_analyzer.analyze_dependencies()

        # Compile comprehensive analysis result
        response.result = {
            "language": language,
            "code_analysis": asdict(code_analysis),
            "self_analysis_summary": self._summarize_analysis_results(analysis_results),
            "dependency_analysis": dependency_stats,
            "semantic_insights": self._extract_semantic_insights(request.code),
            "improvement_suggestions": self._generate_improvement_suggestions(code_analysis, analysis_results)
        }

        # Calculate confidence based on analysis quality
        response.confidence_score = self._calculate_analysis_confidence(analysis_results, code_analysis)

        return response

    def _handle_generate_request(self, request: AssistantRequest, response: AssistantResponse) -> AssistantResponse:
        """Handle code generation request."""
        if not request.prompt:
            raise ValueError("Prompt is required for code generation")

        # Detect target language
        language = request.language or request.context.get("language", "python")

        # Create contextual situation for adaptation
        situation = self._create_contextual_situation(request, "generation")

        # Get adaptation decision
        adaptation = self.adaptive_controller.analyze_situation_and_adapt(situation)
        response.adaptations_made.append(adaptation)

        # Generate code using RAG
        generation_result = self.rag_enhanced_generator.generate_code_with_rag(
            prompt=request.prompt,
            context=None,  # Could be enhanced with CodeContext
            language=language,
            max_thinking_steps=adaptation.adjustments.get("thinking_steps", 3)
        )

        # Execute generated code for validation
        if request.context.get("validate_execution", True):
            execution_result = self.execution_monitor.execute_and_monitor(
                generation_result.generated_code,
                language,
                timeout=adaptation.adjustments.get("timeout_multiplier", 1.0) * 30
            )
            response.execution_results.append(execution_result)

            # If execution failed, try to improve
            if not execution_result.success and adaptation.adjustments.get("retry_attempts", 0) > 0:
                improved_result = self._improve_generated_code(
                    generation_result, execution_result, language, adaptation
                )
                if improved_result:
                    generation_result = improved_result

        response.result = {
            "generated_code": generation_result.generated_code,
            "language": language,
            "generation_details": asdict(generation_result),
            "adaptation_applied": asdict(adaptation),
        }

        response.confidence_score = generation_result.confidence_score

        return response

    def _handle_optimize_request(self, request: AssistantRequest, response: AssistantResponse) -> AssistantResponse:
        """Handle code optimization request."""
        if not request.code:
            raise ValueError("Code is required for optimization")

        language = request.language or self.multi_language_processor.detect_language(request.code)

        # Create optimization targets
        targets = self._create_optimization_targets(request)

        # Run optimization engine
        optimization_results = self.optimization_engine.optimize_iteratively(
            request.code, language, targets
        )

        # Track applied optimizations
        for result in optimization_results:
            if result.success:
                response.optimizations_applied.extend(result.applied_techniques)

        # Analyze impact of optimizations
        if optimization_results:
            final_result = optimization_results[-1]

            # Execute optimized code
            execution_result = self.execution_monitor.execute_and_monitor(
                final_result.optimized_code, language
            )
            response.execution_results.append(execution_result)

            response.result = {
                "original_code": request.code,
                "optimized_code": final_result.optimized_code,
                "optimization_results": [asdict(r) for r in optimization_results],
                "performance_improvement": final_result.improvements,
                "execution_validation": asdict(execution_result)
            }

            response.confidence_score = final_result.confidence_score
        else:
            response.result = {"message": "No optimizations could be applied"}
            response.confidence_score = 0.0

        return response

    def _handle_debug_request(self, request: AssistantRequest, response: AssistantResponse) -> AssistantResponse:
        """Handle debugging request."""
        if not request.code:
            raise ValueError("Code is required for debugging")

        language = request.language or self.multi_language_processor.detect_language(request.code)

        # Execute code to identify issues
        execution_result = self.execution_monitor.execute_and_monitor(request.code, language)
        response.execution_results.append(execution_result)

        # Analyze code for issues
        code_analysis = self.multi_language_processor.analyze_code(request.code, language)

        # Create debugging context
        debug_context = AnalysisContext(
            task_description="Debug and fix code issues",
            code=request.code,
            language=language,
            expected_outcome="Working code without errors",
            constraints=request.constraints,
            optimization_goals=["reliability", "correctness"],
            max_iterations=3,
            timeout_seconds=120.0
        )

        # Run analysis to understand issues
        analysis_results = self.self_analyzer.analyze_and_iterate(debug_context)
        response.analysis_iterations = analysis_results

        # Generate debugging suggestions
        debug_suggestions = self._generate_debug_suggestions(
            execution_result, code_analysis, analysis_results
        )

        response.result = {
            "original_code": request.code,
            "execution_analysis": asdict(execution_result),
            "code_analysis": asdict(code_analysis),
            "debug_suggestions": debug_suggestions,
            "analysis_insights": self._summarize_analysis_results(analysis_results)
        }

        # Calculate confidence based on issue identification
        response.confidence_score = self._calculate_debug_confidence(execution_result, code_analysis)

        return response

    def _handle_refactor_request(self, request: AssistantRequest, response: AssistantResponse) -> AssistantResponse:
        """Handle code refactoring request."""
        if not request.code:
            raise ValueError("Code is required for refactoring")

        language = request.language or self.multi_language_processor.detect_language(request.code)

        # Analyze current code
        code_analysis = self.multi_language_processor.analyze_code(request.code, language)

        # Analyze dependencies and impact
        impact_analysis = self._analyze_refactoring_impact(request.code, language)

        # Create refactoring targets
        refactor_targets = self._create_refactoring_targets(code_analysis, request.context)

        # Apply optimizations focused on maintainability
        optimization_results = self.optimization_engine.optimize_iteratively(
            request.code, language, refactor_targets
        )

        response.result = {
            "original_code": request.code,
            "refactored_code": optimization_results[-1].optimized_code if optimization_results else request.code,
            "refactoring_analysis": asdict(code_analysis),
            "impact_analysis": impact_analysis,
            "applied_refactorings": [r.applied_techniques for r in optimization_results],
            "maintainability_improvement": optimization_results[-1].improvements if optimization_results else {}
        }

        response.confidence_score = optimization_results[-1].confidence_score if optimization_results else 0.5

        return response

    def _handle_general_request(self, request: AssistantRequest, response: AssistantResponse) -> AssistantResponse:
        """Handle general requests."""
        # For general requests, provide comprehensive analysis
        if request.code:
            # Analyze the code
            language = request.language or self.multi_language_processor.detect_language(request.code)
            code_analysis = self.multi_language_processor.analyze_code(request.code, language)

            response.result = {
                "code_analysis": asdict(code_analysis),
                "language": language,
                "general_insights": self._extract_general_insights(request.code, code_analysis)
            }

            response.confidence_score = 0.7
        else:
            response.result = {"message": "Please provide code or a specific request type"}
            response.confidence_score = 0.0

        return response

    def _create_contextual_situation(self, request: AssistantRequest, task_type: str) -> ContextualSituation:
        """Create contextual situation for adaptive control."""
        # Get historical performance for this type of request
        similar_requests = [r for r in self.response_history
                           if r.request_id != request.request_id and task_type in str(r)]

        performance_history = [r.confidence_score for r in similar_requests[-10:]]
        error_history = []

        for r in similar_requests[-5:]:
            if hasattr(r.result, 'errors') and r.result.errors:
                error_history.extend(r.result.errors[:2])  # Limit error history

        success_rate = sum(1 for r in similar_requests[-10:] if r.success) / max(len(similar_requests[-10:]), 1)

        return ContextualSituation(
            task_type=task_type,
            language=request.language or "python",
            complexity_level=request.context.get("complexity", 0.5),
            error_history=error_history,
            performance_history=performance_history,
            success_rate=success_rate,
            user_preferences=request.preferences,
            environmental_factors={"workspace_size": len(list(self.workspace_dir.rglob("*.py")))}
        )

    def _create_optimization_targets(self, request: AssistantRequest) -> List[OptimizationTarget]:
        """Create optimization targets from request."""
        targets = []

        # Default targets
        targets.append(OptimizationTarget(
            type=OptimizationType.PERFORMANCE,
            current_value=0.5,
            target_value=0.8,
            priority=0.8,
            constraints=request.constraints,
            metrics={}
        ))

        targets.append(OptimizationTarget(
            type=OptimizationType.READABILITY,
            current_value=0.5,
            target_value=0.8,
            priority=0.7,
            constraints=request.constraints,
            metrics={}
        ))

        # Add specific targets from context
        if "performance" in request.context:
            targets.append(OptimizationTarget(
                type=OptimizationType.EFFICIENCY,
                current_value=0.5,
                target_value=0.9,
                priority=0.9,
                constraints=request.constraints,
                metrics={}
            ))

        return targets

    def _create_refactoring_targets(self, code_analysis: CodeAnalysisResult, context: Dict[str, Any]) -> List[OptimizationTarget]:
        """Create refactoring-specific optimization targets."""
        targets = []

        # Focus on maintainability and readability
        targets.append(OptimizationTarget(
            type=OptimizationType.MAINTAINABILITY,
            current_value=code_analysis.maintainability_score,
            target_value=0.9,
            priority=0.9,
            constraints=[],
            metrics={}
        ))

        targets.append(OptimizationTarget(
            type=OptimizationType.READABILITY,
            current_value=code_analysis.maintainability_score,  # Use as proxy
            target_value=0.85,
            priority=0.8,
            constraints=[],
            metrics={}
        ))

        if code_analysis.complexity_score > 0.7:
            targets.append(OptimizationTarget(
                type=OptimizationType.COMPLEXITY,
                current_value=code_analysis.complexity_score,
                target_value=0.5,
                priority=0.7,
                constraints=[],
                metrics={}
            ))

        return targets

    def _improve_generated_code(self, generation_result: GenerationResult,
                               execution_result: ExecutionResult, language: str,
                               adaptation: AdaptationDecision) -> Optional[GenerationResult]:
        """Improve generated code based on execution results."""
        if execution_result.success:
            return None

        # Create improvement prompt
        improvement_prompt = f"Fix the following {language} code that has errors:\n\n"
        improvement_prompt += f"Code:\n{generation_result.generated_code}\n\n"
        improvement_prompt += f"Errors:\n{chr(10).join(execution_result.errors)}\n\n"
        improvement_prompt += "Please provide a corrected version."

        # Generate improved code
        try:
            improved_result = self.rag_enhanced_generator.generate_code_with_rag(
                prompt=improvement_prompt,
                language=language,
                max_thinking_steps=2
            )
            return improved_result
        except Exception as e:
            logger.error(f"Error improving generated code: {e}")
            return None

    def _summarize_analysis_results(self, analysis_results: List[AnalysisResult]) -> Dict[str, Any]:
        """Summarize analysis results."""
        if not analysis_results:
            return {}

        successful_iterations = [r for r in analysis_results if r.success]

        return {
            "total_iterations": len(analysis_results),
            "successful_iterations": len(successful_iterations),
            "final_confidence": analysis_results[-1].confidence_score,
            "average_execution_time": sum(r.execution_time for r in analysis_results) / len(analysis_results),
            "key_insights": [insight for r in analysis_results for insight in r.insights],
            "common_issues": self._extract_common_issues(analysis_results)
        }

    def _extract_semantic_insights(self, code: str) -> List[str]:
        """Extract semantic insights from code."""
        insights = []

        # Use semantic indexer to find similar code
        try:
            search_results = self.semantic_indexer.search_semantic(
                "similar code patterns", max_results=3
            )

            if search_results:
                insights.append(f"Found {len(search_results)} similar code patterns in codebase")
                for result in search_results[:2]:
                    insights.append(f"Similar pattern: {result.context_match}")
        except Exception as e:
            logger.warning(f"Error extracting semantic insights: {e}")

        return insights

    def _generate_improvement_suggestions(self, code_analysis: CodeAnalysisResult,
                                        analysis_results: List[AnalysisResult]) -> List[str]:
        """Generate improvement suggestions."""
        suggestions = []

        # Based on code analysis
        if code_analysis.complexity_score > 0.7:
            suggestions.append("Consider breaking down complex functions into smaller ones")

        if code_analysis.maintainability_score < 0.6:
            suggestions.append("Add more documentation and improve code structure")

        if code_analysis.security_issues:
            suggestions.append("Address security issues found in the code")

        # Based on analysis results
        if analysis_results:
            final_result = analysis_results[-1]
            if final_result.confidence_score < 0.7:
                suggestions.append("Code analysis confidence is low - consider manual review")

        return suggestions

    def _calculate_analysis_confidence(self, analysis_results: List[AnalysisResult],
                                     code_analysis: CodeAnalysisResult) -> float:
        """Calculate confidence score for analysis."""
        if not analysis_results:
            return 0.5

        # Base confidence on analysis results
        analysis_confidence = analysis_results[-1].confidence_score

        # Adjust based on code quality
        quality_factor = (code_analysis.maintainability_score +
                         (1.0 - code_analysis.complexity_score)) / 2

        # Combine confidences
        overall_confidence = (analysis_confidence * 0.7 + quality_factor * 0.3)

        return max(0.0, min(1.0, overall_confidence))

    def _calculate_debug_confidence(self, execution_result: ExecutionResult,
                                  code_analysis: CodeAnalysisResult) -> float:
        """Calculate confidence score for debugging."""
        base_confidence = 0.5

        # Higher confidence if we can identify specific issues
        if execution_result.errors:
            base_confidence += 0.3  # We found concrete errors

        if code_analysis.security_issues or code_analysis.style_violations:
            base_confidence += 0.2  # We found code quality issues

        # Lower confidence if syntax is invalid
        if not code_analysis.syntax_valid:
            base_confidence -= 0.2

        return max(0.0, min(1.0, base_confidence))

    def _analyze_refactoring_impact(self, code: str, language: str) -> Dict[str, Any]:
        """Analyze the impact of refactoring."""
        # Simple impact analysis
        lines_count = len(code.splitlines())
        function_count = code.count("def ") if language == "python" else code.count("function ")

        return {
            "code_size": lines_count,
            "function_count": function_count,
            "estimated_impact": "medium" if lines_count > 50 else "low",
            "risk_level": "low",  # Could be enhanced with dependency analysis
        }

    def _generate_debug_suggestions(self, execution_result: ExecutionResult,
                                  code_analysis: CodeAnalysisResult,
                                  analysis_results: List[AnalysisResult]) -> List[str]:
        """Generate debugging suggestions."""
        suggestions = []

        # Based on execution errors
        for error in execution_result.errors:
            if "syntax" in error.lower():
                suggestions.append("Fix syntax errors in the code")
            elif "import" in error.lower():
                suggestions.append("Check import statements and dependencies")
            elif "name" in error.lower() and "not defined" in error.lower():
                suggestions.append("Check for undefined variables or functions")

        # Based on code analysis
        if not code_analysis.syntax_valid:
            suggestions.append("Resolve syntax errors before execution")

        if code_analysis.security_issues:
            suggestions.append("Address security vulnerabilities")

        return suggestions

    def _extract_general_insights(self, code: str, code_analysis: CodeAnalysisResult) -> List[str]:
        """Extract general insights about the code."""
        insights = []

        insights.append(f"Code contains {len(code_analysis.functions)} functions and {len(code_analysis.classes)} classes")

        if code_analysis.complexity_score > 0.7:
            insights.append("Code has high complexity - consider simplification")
        elif code_analysis.complexity_score < 0.3:
            insights.append("Code has low complexity - well structured")

        if code_analysis.dependencies:
            insights.append(f"Code depends on {len(code_analysis.dependencies)} external modules")

        return insights

    def _extract_common_issues(self, analysis_results: List[AnalysisResult]) -> List[str]:
        """Extract common issues from analysis results."""
        all_errors = []
        for result in analysis_results:
            all_errors.extend(result.errors)

        # Count error frequency
        error_counts = {}
        for error in all_errors:
            error_type = error.split(":")[0] if ":" in error else error
            error_counts[error_type] = error_counts.get(error_type, 0) + 1

        # Return most common issues
        common_issues = sorted(error_counts.items(), key=lambda x: x[1], reverse=True)
        return [issue[0] for issue in common_issues[:3]]

    def _learn_from_interaction(self, request: AssistantRequest, response: AssistantResponse):
        """Learn from the interaction to improve future performance."""
        # Extract learning insights
        if response.success and response.confidence_score > 0.8:
            insight = f"Successful {request.request_type} request with high confidence"
            self.learning_insights.append(insight)

        # Update performance metrics
        request_type = request.request_type
        if request_type not in self.performance_metrics:
            self.performance_metrics[request_type] = {
                "total_requests": 0,
                "successful_requests": 0,
                "average_confidence": 0.0,
                "average_processing_time": 0.0
            }

        metrics = self.performance_metrics[request_type]
        metrics["total_requests"] += 1
        if response.success:
            metrics["successful_requests"] += 1

        # Update averages
        total = metrics["total_requests"]
        metrics["average_confidence"] = (
            (metrics["average_confidence"] * (total - 1) + response.confidence_score) / total
        )
        metrics["average_processing_time"] = (
            (metrics["average_processing_time"] * (total - 1) + response.processing_time) / total
        )

        logger.info(f"Updated learning metrics for {request_type}")

    def get_assistant_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about the assistant."""
        return {
            "total_requests": len(self.request_history),
            "total_responses": len(self.response_history),
            "performance_metrics": self.performance_metrics,
            "learning_insights_count": len(self.learning_insights),
            "component_statistics": {
                "semantic_indexer": {"symbols_indexed": len(self.semantic_indexer.symbols)},
                "rag_generator": self.rag_enhanced_generator.get_generation_statistics(),
                "dependency_analyzer": {"dependencies_tracked": len(self.dependency_analyzer.dependencies)},
            }
        }

    def clear_history(self):
        """Clear request and response history."""
        with self.lock:
            self.request_history.clear()
            self.response_history.clear()
            self.learning_insights.clear()
            logger.info("Cleared assistant history")