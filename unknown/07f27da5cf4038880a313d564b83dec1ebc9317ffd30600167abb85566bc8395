"""
Iterative Enhanced Agent with Advanced Master Controller Integration.
Provides iterative analysis and self-improvement capabilities.
"""

import time
import logging
import threading
from typing import Dict, List, Optional, Any, Callable, Union, Generator
from pathlib import Path
import json
import uuid

from .advanced_master_controller import AdvancedMasterController, TaskType, Priority, TaskResult
from .self_analyzing_intelligence import SelfA<PERSON>yzingIntelligence, AnalysisType

logger = logging.getLogger(__name__)

class IterativeEnhancedAgent:
    """Enhanced agent with iterative analysis and self-improvement capabilities."""

    def __init__(self, model_manager, conversation_manager, workspace_dir: Path):
        """Initialize the iterative enhanced agent.
        
        Args:
            model_manager: The model manager for AI operations
            conversation_manager: The conversation manager
            workspace_dir: The workspace directory
        """
        self.model_manager = model_manager
        self.conversation_manager = conversation_manager
        self.workspace_dir = workspace_dir
        
        # Initialize master controller
        self.master_controller = AdvancedMasterController(model_manager, workspace_dir)
        
        # Enhanced capabilities
        self.auto_improvement_enabled = True
        self.context_awareness_enabled = True
        self.iterative_analysis_enabled = True
        
        # Performance tracking
        self.session_metrics = {
            "requests_processed": 0,
            "average_response_time": 0.0,
            "average_confidence": 0.0,
            "improvement_rate": 0.0,
            "iterations_performed": 0,
            "success_rate": 0.0
        }
        
        # Callback system
        self.response_callbacks: List[Callable[[str, Dict[str, Any]], None]] = []
        self.iteration_callbacks: List[Callable[[int, Dict[str, Any]], None]] = []
        
        # Setup master controller callbacks
        self.master_controller.add_iteration_callback(self._on_iteration_complete)
        self.master_controller.add_task_callback(self._on_task_complete)
        
        # Start the master controller
        self.master_controller.start()
        
        logger.info("Iterative enhanced agent initialized successfully")

    def process_message(self, message: str, conversation=None, enable_iterative_improvement: bool = True) -> str:
        """Process a message with iterative analysis and improvement.
        
        Args:
            message: The user message
            conversation: The conversation context
            enable_iterative_improvement: Whether to enable iterative improvement
            
        Returns:
            The enhanced response
        """
        start_time = time.time()
        
        try:
            # Get conversation context
            if conversation is None:
                if self.conversation_manager.current_conversation is None:
                    self.conversation_manager.new_conversation()
                conversation = self.conversation_manager.current_conversation
            
            # Add user message to conversation
            conversation.add_message("user", message)
            
            # Analyze message intent and context
            context = self._analyze_message_context(message, conversation)
            context["enable_iterative_improvement"] = enable_iterative_improvement and self.iterative_analysis_enabled
            
            # Process with master controller
            task_id = self.master_controller.process_natural_language_request(
                request=message,
                context=context
            )
            
            # Wait for task completion with timeout
            result = self._wait_for_task_completion(task_id, timeout=120.0)
            
            if result and result.success:
                # Format response with iteration information
                response = self._format_enhanced_response(
                    result.result, 
                    result.confidence_score, 
                    result.recommendations,
                    result.iterations_performed,
                    result.improvement_achieved
                )
                
                # Add assistant message to conversation
                conversation.add_message("assistant", response)
                
                # Update session metrics
                self._update_session_metrics(result, time.time() - start_time)
                
                # Trigger callbacks
                for callback in self.response_callbacks:
                    try:
                        callback(response, {
                            "confidence": result.confidence_score,
                            "task_id": task_id,
                            "iterations": result.iterations_performed,
                            "improvement": result.improvement_achieved
                        })
                    except Exception as e:
                        logger.error(f"Error in response callback: {e}")
                
                return response
            else:
                error_response = f"I encountered an issue processing your request. {result.result.get('error', '') if result else 'Task failed to complete.'}"
                conversation.add_message("assistant", error_response)
                return error_response
                
        except Exception as e:
            logger.error(f"Error processing message: {e}", exc_info=True)
            error_response = f"I encountered an unexpected error: {str(e)}"
            if conversation:
                conversation.add_message("assistant", error_response)
            return error_response

    def stream_process_message(self, message: str, conversation=None, callback=None) -> Generator[str, None, None]:
        """Stream process a message with enhanced capabilities.
        
        Args:
            message: The user message
            conversation: The conversation context
            callback: Optional callback for each chunk
            
        Yields:
            Response chunks
        """
        response = self.process_message(message, conversation)
        
        # Simulate streaming by yielding chunks
        chunk_size = 50
        for i in range(0, len(response), chunk_size):
            chunk = response[i:i + chunk_size]
            if callback:
                callback(chunk)
            yield chunk
            time.sleep(0.01)

    def _analyze_message_context(self, message: str, conversation) -> Dict[str, Any]:
        """Analyze message context for enhanced processing."""
        context = {
            "message_length": len(message),
            "conversation_length": len(conversation.messages) if conversation else 0,
            "timestamp": time.time(),
            "workspace_dir": str(self.workspace_dir)
        }
        
        # Analyze message intent
        intent_analysis = self._analyze_intent(message)
        context.update(intent_analysis)
        
        # Add conversation history context
        if conversation and len(conversation.messages) > 1:
            recent_messages = conversation.messages[-5:]
            context["recent_context"] = [
                {"role": msg.role, "content": msg.content[:200]} 
                for msg in recent_messages
            ]
        
        # Add workspace context
        workspace_context = self._get_workspace_context()
        context.update(workspace_context)
        
        return context

    def _analyze_intent(self, message: str) -> Dict[str, Any]:
        """Analyze the intent of a message."""
        intent_keywords = {
            "code_analysis": ["analyze", "review", "check", "examine", "inspect"],
            "code_generation": ["create", "generate", "write", "build", "implement"],
            "debugging": ["debug", "fix", "error", "bug", "issue", "problem"],
            "optimization": ["optimize", "improve", "faster", "efficient", "performance"],
            "refactoring": ["refactor", "restructure", "reorganize", "clean"],
            "testing": ["test", "unit test", "integration test", "validate"],
            "documentation": ["document", "comment", "explain", "describe"],
            "search": ["find", "search", "locate", "look for"],
            "execution": ["run", "execute", "compile", "build"]
        }
        
        message_lower = message.lower()
        detected_intents = []
        
        for intent, keywords in intent_keywords.items():
            if any(keyword in message_lower for keyword in keywords):
                detected_intents.append(intent)
        
        return {
            "detected_intents": detected_intents,
            "primary_intent": detected_intents[0] if detected_intents else "general",
            "complexity_estimate": self._estimate_complexity(message)
        }

    def _estimate_complexity(self, message: str) -> str:
        """Estimate the complexity of a request."""
        if len(message) < 50:
            return "low"
        elif len(message) < 200:
            return "medium"
        else:
            return "high"

    def _get_workspace_context(self) -> Dict[str, Any]:
        """Get context about the current workspace."""
        context = {}
        
        try:
            if self.workspace_dir.exists():
                context["workspace_exists"] = True
                context["workspace_files"] = len(list(self.workspace_dir.rglob("*")))
                
                # Check for common project files
                project_indicators = {
                    "python": ["requirements.txt", "setup.py", "pyproject.toml"],
                    "javascript": ["package.json", "yarn.lock"],
                    "java": ["pom.xml", "build.gradle"],
                    "csharp": ["*.csproj", "*.sln"],
                    "rust": ["Cargo.toml"],
                    "go": ["go.mod"]
                }
                
                detected_languages = []
                for lang, indicators in project_indicators.items():
                    for indicator in indicators:
                        if list(self.workspace_dir.glob(indicator)):
                            detected_languages.append(lang)
                            break
                
                context["detected_languages"] = detected_languages
            else:
                context["workspace_exists"] = False
                
        except Exception as e:
            logger.error(f"Error getting workspace context: {e}")
            context["workspace_error"] = str(e)
        
        return context

    def _wait_for_task_completion(self, task_id: str, timeout: float = 60.0) -> Optional[TaskResult]:
        """Wait for a task to complete."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            result = self.master_controller.get_task_result(task_id)
            if result:
                return result
            time.sleep(0.1)
        
        logger.warning(f"Task {task_id} timed out after {timeout} seconds")
        return None

    def _format_enhanced_response(self, result: Any, confidence: float, recommendations: List[str], 
                                iterations: int, improvement: float) -> str:
        """Format an enhanced response with iteration information."""
        if isinstance(result, dict) and "error" in result:
            return f"Error: {result['error']}"
        
        response_parts = []
        
        # Main result
        if isinstance(result, dict):
            if "generated_code" in result:
                response_parts.append(f"```{result.get('language', 'python')}\n{result['generated_code']}\n```")
            elif "analysis" in result:
                response_parts.append(f"Analysis: {result['analysis']}")
            else:
                response_parts.append(str(result))
        else:
            response_parts.append(str(result))
        
        # Add iteration information if applicable
        if iterations > 0:
            response_parts.append(f"\n*Iterative Analysis: {iterations} iterations performed, {improvement:.2f} improvement achieved*")
        
        # Add confidence and recommendations
        response_parts.append(f"\n*Confidence: {confidence:.2f}*")
        
        if recommendations:
            response_parts.append(f"\n**Recommendations:**")
            for rec in recommendations[:3]:  # Limit to top 3
                response_parts.append(f"- {rec}")
        
        return "\n".join(response_parts)

    def _update_session_metrics(self, result: TaskResult, execution_time: float):
        """Update session metrics."""
        self.session_metrics["requests_processed"] += 1
        
        if result.success:
            total = self.session_metrics["requests_processed"]
            self.session_metrics["average_response_time"] = (
                (self.session_metrics["average_response_time"] * (total - 1) + execution_time) / total
            )
            self.session_metrics["average_confidence"] = (
                (self.session_metrics["average_confidence"] * (total - 1) + result.confidence_score) / total
            )
            self.session_metrics["iterations_performed"] += result.iterations_performed
            self.session_metrics["improvement_rate"] = (
                (self.session_metrics["improvement_rate"] * (total - 1) + result.improvement_achieved) / total
            )
        
        self.session_metrics["success_rate"] = (
            sum(1 for r in [result] if r.success) / self.session_metrics["requests_processed"]
        )

    def _on_iteration_complete(self, task_id: str, iteration: int, data: Dict[str, Any]):
        """Callback for iteration completion."""
        logger.info(f"Iteration {iteration} complete for task {task_id}")
        
        for callback in self.iteration_callbacks:
            try:
                callback(iteration, data)
            except Exception as e:
                logger.error(f"Error in iteration callback: {e}")

    def _on_task_complete(self, result: TaskResult):
        """Callback for task completion."""
        logger.info(f"Task {result.task_id} completed with confidence {result.confidence_score:.2f}")

    def get_status(self) -> Dict[str, Any]:
        """Get current status of the agent."""
        return {
            "master_controller_status": self.master_controller.get_status(),
            "session_metrics": self.session_metrics.copy(),
            "capabilities": {
                "auto_improvement": self.auto_improvement_enabled,
                "context_awareness": self.context_awareness_enabled,
                "iterative_analysis": self.iterative_analysis_enabled
            }
        }

    def analyze_code(self, code: str, language: str = "python") -> Dict[str, Any]:
        """Analyze code using the AI code assistant."""
        from core.ai_code_assistant import AssistantRequest
        request = AssistantRequest(
            request_id=str(uuid.uuid4()),
            request_type="analyze",
            code=code,
            language=language,
            prompt="Analyze this code",
            context={},
            preferences={},
            constraints=[]
        )
        response = self.master_controller.ai_assistant.process_request(request)
        return response.result if response.success else {"error": response.result.get("error", "Analysis failed")}

    def analyze_performance(self, code: str, language: str = "python"):
        """Analyze code performance."""
        return self.master_controller.performance_analyzer.analyze_performance(code, language)

    def generate_code(self, description: str, language: str = "python") -> str:
        """Generate code based on description."""
        from core.ai_code_assistant import AssistantRequest
        request = AssistantRequest(
            request_id=str(uuid.uuid4()),
            request_type="generate",
            code="",
            language=language,
            prompt=description,
            context={},
            preferences={},
            constraints=[]
        )
        response = self.master_controller.ai_assistant.process_request(request)
        if response.success:
            return response.result.get("generated_code", "")
        return f"Error: {response.result.get('error', 'Generation failed')}"

    def refactor_code(self, code: str, language: str = "python", focus_areas: List[str] = None) -> str:
        """Refactor code for improvement."""
        return self.master_controller.refactoring_engine.refactor_code(code, language, focus_areas or [])

    def complete_code(self, partial_code: str, language: str = "python") -> str:
        """Complete partial code."""
        return self.master_controller.context_completion.complete_code(partial_code, language)

    def get_learning_metrics(self):
        """Get learning system metrics."""
        return self.master_controller.learning_system.get_learning_metrics()

    def index_codebase(self, directory: Path):
        """Index codebase for semantic search."""
        self.master_controller.semantic_indexer.index_directory(directory)

    def search_code(self, query: str) -> List[Dict[str, Any]]:
        """Search code semantically."""
        return self.master_controller.semantic_indexer.search(query)

    def get_enhanced_response(self, query: str, context: Dict[str, Any] = None) -> str:
        """Get RAG-enhanced response."""
        return self.master_controller.rag_system.get_enhanced_response(query, context or {})

    def perform_self_analysis(self) -> Dict[str, Any]:
        """Perform self-analysis."""
        return self.master_controller.intelligence.analyze_system_state()

    def stop(self):
        """Stop the agent."""
        self.master_controller.stop()
        logger.info("Iterative enhanced agent stopped")
