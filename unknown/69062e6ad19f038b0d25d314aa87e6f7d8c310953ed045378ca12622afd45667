"""
Error Detector for the AI Code Assistant.

This module provides automatic error detection and resolution capabilities,
analyzing code for potential issues and suggesting fixes.
"""

import time
import logging
import threading
import ast
import re
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class CodeError:
    """Represents a detected code error."""
    error_type: str
    severity: str  # critical, high, medium, low
    message: str
    file_path: str
    line_number: int
    column_number: int
    error_code: str
    category: str  # syntax, logic, style, security, performance
    confidence: float
    suggested_fix: Optional[str]
    explanation: str

@dataclass
class ErrorPattern:
    """Represents an error detection pattern."""
    pattern_id: str
    pattern_type: str
    regex_pattern: Optional[str]
    ast_pattern: Optional[str]
    description: str
    severity: str
    category: str
    fix_template: Optional[str]

@dataclass
class ErrorDetectionResult:
    """Result of error detection analysis."""
    errors: List[CodeError]
    warnings: List[CodeError]
    suggestions: List[CodeError]
    analysis_time: float
    total_issues: int
    critical_issues: int
    fixable_issues: int

class ErrorDetector:
    """Automatic error detection and resolution system."""

    def __init__(self, workspace_dir: Path, model_manager=None):
        """Initialize the error detector.
        
        Args:
            workspace_dir: The workspace directory
            model_manager: Optional model manager for AI-assisted error detection
        """
        self.workspace_dir = workspace_dir
        self.model_manager = model_manager
        self.error_patterns: Dict[str, List[ErrorPattern]] = {}
        self.detection_history: List[ErrorDetectionResult] = []
        self.custom_rules: List[ErrorPattern] = []
        self.lock = threading.RLock()
        
        # Initialize error patterns for different languages
        self._initialize_error_patterns()

    def _initialize_error_patterns(self):
        """Initialize error detection patterns for different languages."""
        # Python error patterns
        self.error_patterns["python"] = [
            ErrorPattern(
                pattern_id="py_undefined_variable",
                pattern_type="ast",
                regex_pattern=None,
                ast_pattern="Name",
                description="Undefined variable usage",
                severity="high",
                category="logic",
                fix_template="Define the variable before using it"
            ),
            ErrorPattern(
                pattern_id="py_import_error",
                pattern_type="regex",
                regex_pattern=r"import\s+(\w+)",
                ast_pattern=None,
                description="Potential import error",
                severity="medium",
                category="syntax",
                fix_template="Check if module '{module}' is installed"
            ),
            ErrorPattern(
                pattern_id="py_bare_except",
                pattern_type="regex",
                regex_pattern=r"except\s*:",
                ast_pattern=None,
                description="Bare except clause",
                severity="medium",
                category="style",
                fix_template="Specify exception type: except SpecificException:"
            ),
            ErrorPattern(
                pattern_id="py_unused_import",
                pattern_type="ast",
                regex_pattern=None,
                ast_pattern="Import",
                description="Unused import",
                severity="low",
                category="style",
                fix_template="Remove unused import"
            ),
            ErrorPattern(
                pattern_id="py_sql_injection",
                pattern_type="regex",
                regex_pattern=r"execute\s*\(\s*[\"'].*%.*[\"']\s*%",
                ast_pattern=None,
                description="Potential SQL injection vulnerability",
                severity="critical",
                category="security",
                fix_template="Use parameterized queries instead"
            )
        ]
        
        # JavaScript error patterns
        self.error_patterns["javascript"] = [
            ErrorPattern(
                pattern_id="js_var_usage",
                pattern_type="regex",
                regex_pattern=r"\bvar\s+",
                ast_pattern=None,
                description="Use of 'var' instead of 'let' or 'const'",
                severity="medium",
                category="style",
                fix_template="Replace 'var' with 'let' or 'const'"
            ),
            ErrorPattern(
                pattern_id="js_equality_operator",
                pattern_type="regex",
                regex_pattern=r"==(?!=)",
                ast_pattern=None,
                description="Use of '==' instead of '==='",
                severity="medium",
                category="logic",
                fix_template="Use '===' for strict equality"
            ),
            ErrorPattern(
                pattern_id="js_eval_usage",
                pattern_type="regex",
                regex_pattern=r"\beval\s*\(",
                ast_pattern=None,
                description="Use of eval() function",
                severity="critical",
                category="security",
                fix_template="Avoid using eval() - use safer alternatives"
            )
        ]
        
        # Generic patterns for all languages
        self.error_patterns["generic"] = [
            ErrorPattern(
                pattern_id="long_line",
                pattern_type="regex",
                regex_pattern=r".{121,}",
                ast_pattern=None,
                description="Line too long",
                severity="low",
                category="style",
                fix_template="Break line into multiple lines"
            ),
            ErrorPattern(
                pattern_id="trailing_whitespace",
                pattern_type="regex",
                regex_pattern=r"\s+$",
                ast_pattern=None,
                description="Trailing whitespace",
                severity="low",
                category="style",
                fix_template="Remove trailing whitespace"
            )
        ]

    def detect_errors(self, code: str, language: str = "python", file_path: str = "") -> ErrorDetectionResult:
        """Detect errors in the given code.
        
        Args:
            code: The code to analyze
            language: Programming language
            file_path: Optional file path for context
            
        Returns:
            Error detection result
        """
        with self.lock:
            start_time = time.time()
            logger.info(f"Detecting errors in {language} code")
            
            all_issues = []
            
            # Apply language-specific patterns
            if language in self.error_patterns:
                issues = self._apply_error_patterns(code, self.error_patterns[language], file_path)
                all_issues.extend(issues)
            
            # Apply generic patterns
            generic_issues = self._apply_error_patterns(code, self.error_patterns["generic"], file_path)
            all_issues.extend(generic_issues)
            
            # Language-specific analysis
            if language == "python":
                python_issues = self._analyze_python_errors(code, file_path)
                all_issues.extend(python_issues)
            elif language in ["javascript", "typescript"]:
                js_issues = self._analyze_javascript_errors(code, file_path)
                all_issues.extend(js_issues)
            
            # Apply custom rules
            custom_issues = self._apply_error_patterns(code, self.custom_rules, file_path)
            all_issues.extend(custom_issues)
            
            # Categorize issues
            errors = [issue for issue in all_issues if issue.severity in ["critical", "high"]]
            warnings = [issue for issue in all_issues if issue.severity == "medium"]
            suggestions = [issue for issue in all_issues if issue.severity == "low"]
            
            analysis_time = time.time() - start_time
            
            result = ErrorDetectionResult(
                errors=errors,
                warnings=warnings,
                suggestions=suggestions,
                analysis_time=analysis_time,
                total_issues=len(all_issues),
                critical_issues=len([i for i in all_issues if i.severity == "critical"]),
                fixable_issues=len([i for i in all_issues if i.suggested_fix])
            )
            
            # Store in history
            self.detection_history.append(result)
            
            logger.info(f"Detected {len(all_issues)} issues in {analysis_time:.3f}s")
            return result

    def _apply_error_patterns(self, code: str, patterns: List[ErrorPattern], file_path: str) -> List[CodeError]:
        """Apply error patterns to detect issues."""
        issues = []
        lines = code.splitlines()
        
        for pattern in patterns:
            if pattern.pattern_type == "regex" and pattern.regex_pattern:
                issues.extend(self._apply_regex_pattern(pattern, code, lines, file_path))
            elif pattern.pattern_type == "ast" and pattern.ast_pattern:
                issues.extend(self._apply_ast_pattern(pattern, code, file_path))
        
        return issues

    def _apply_regex_pattern(self, pattern: ErrorPattern, code: str, lines: List[str], file_path: str) -> List[CodeError]:
        """Apply regex-based error pattern."""
        issues = []
        
        for line_num, line in enumerate(lines, 1):
            matches = re.finditer(pattern.regex_pattern, line)
            for match in matches:
                issue = CodeError(
                    error_type=pattern.pattern_id,
                    severity=pattern.severity,
                    message=pattern.description,
                    file_path=file_path,
                    line_number=line_num,
                    column_number=match.start(),
                    error_code=pattern.pattern_id.upper(),
                    category=pattern.category,
                    confidence=0.8,
                    suggested_fix=pattern.fix_template,
                    explanation=f"Pattern matched: {pattern.description}"
                )
                issues.append(issue)
        
        return issues

    def _apply_ast_pattern(self, pattern: ErrorPattern, code: str, file_path: str) -> List[CodeError]:
        """Apply AST-based error pattern."""
        issues = []
        
        try:
            tree = ast.parse(code)
            
            if pattern.pattern_id == "py_undefined_variable":
                issues.extend(self._check_undefined_variables(tree, file_path))
            elif pattern.pattern_id == "py_unused_import":
                issues.extend(self._check_unused_imports(tree, code, file_path))
        
        except SyntaxError:
            # Skip AST analysis for invalid syntax
            pass
        
        return issues

    def _analyze_python_errors(self, code: str, file_path: str) -> List[CodeError]:
        """Analyze Python-specific errors."""
        issues = []
        
        try:
            # Try to parse the code
            ast.parse(code)
        except SyntaxError as e:
            issue = CodeError(
                error_type="syntax_error",
                severity="critical",
                message=f"Syntax error: {e.msg}",
                file_path=file_path,
                line_number=e.lineno or 1,
                column_number=e.offset or 0,
                error_code="SYNTAX_ERROR",
                category="syntax",
                confidence=1.0,
                suggested_fix="Fix the syntax error",
                explanation=f"Python syntax error: {e.msg}"
            )
            issues.append(issue)
        
        # Check for common Python issues
        issues.extend(self._check_python_style_issues(code, file_path))
        issues.extend(self._check_python_logic_issues(code, file_path))
        
        return issues

    def _analyze_javascript_errors(self, code: str, file_path: str) -> List[CodeError]:
        """Analyze JavaScript-specific errors."""
        issues = []
        
        # Check for common JavaScript issues
        lines = code.splitlines()
        
        for line_num, line in enumerate(lines, 1):
            # Check for missing semicolons (simple heuristic)
            if (line.strip() and 
                not line.strip().endswith((';', '{', '}', ')', ',')) and
                not line.strip().startswith(('if', 'for', 'while', 'function', 'class', '//'))):
                
                issue = CodeError(
                    error_type="missing_semicolon",
                    severity="low",
                    message="Missing semicolon",
                    file_path=file_path,
                    line_number=line_num,
                    column_number=len(line),
                    error_code="MISSING_SEMICOLON",
                    category="style",
                    confidence=0.6,
                    suggested_fix="Add semicolon at end of line",
                    explanation="JavaScript statements should end with semicolons"
                )
                issues.append(issue)
        
        return issues

    def _check_undefined_variables(self, tree: ast.AST, file_path: str) -> List[CodeError]:
        """Check for undefined variables in Python AST."""
        issues = []
        defined_names = set()
        used_names = []
        
        # Collect defined names
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                defined_names.add(node.name)
            elif isinstance(node, ast.ClassDef):
                defined_names.add(node.name)
            elif isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        defined_names.add(target.id)
        
        # Collect used names
        for node in ast.walk(tree):
            if isinstance(node, ast.Name) and isinstance(node.ctx, ast.Load):
                used_names.append((node.id, node.lineno, node.col_offset))
        
        # Check for undefined usage
        builtins = {'print', 'len', 'str', 'int', 'list', 'dict', 'range', 'open', 'type', 'isinstance'}
        
        for name, line_num, col_offset in used_names:
            if name not in defined_names and name not in builtins:
                issue = CodeError(
                    error_type="undefined_variable",
                    severity="high",
                    message=f"Undefined variable: {name}",
                    file_path=file_path,
                    line_number=line_num,
                    column_number=col_offset,
                    error_code="UNDEFINED_VAR",
                    category="logic",
                    confidence=0.7,
                    suggested_fix=f"Define variable '{name}' before using it",
                    explanation=f"Variable '{name}' is used but not defined"
                )
                issues.append(issue)
        
        return issues

    def _check_unused_imports(self, tree: ast.AST, code: str, file_path: str) -> List[CodeError]:
        """Check for unused imports in Python AST."""
        issues = []
        imported_names = set()
        used_names = set()
        
        # Collect imported names
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    name = alias.asname if alias.asname else alias.name
                    imported_names.add((name, node.lineno))
            elif isinstance(node, ast.ImportFrom):
                for alias in node.names:
                    name = alias.asname if alias.asname else alias.name
                    imported_names.add((name, node.lineno))
        
        # Collect used names
        for node in ast.walk(tree):
            if isinstance(node, ast.Name):
                used_names.add(node.id)
        
        # Check for unused imports
        for name, line_num in imported_names:
            if name not in used_names:
                issue = CodeError(
                    error_type="unused_import",
                    severity="low",
                    message=f"Unused import: {name}",
                    file_path=file_path,
                    line_number=line_num,
                    column_number=0,
                    error_code="UNUSED_IMPORT",
                    category="style",
                    confidence=0.8,
                    suggested_fix=f"Remove unused import '{name}'",
                    explanation=f"Import '{name}' is not used in the code"
                )
                issues.append(issue)
        
        return issues

    def _check_python_style_issues(self, code: str, file_path: str) -> List[CodeError]:
        """Check for Python style issues."""
        issues = []
        lines = code.splitlines()
        
        for line_num, line in enumerate(lines, 1):
            # Check for inconsistent indentation
            if line.strip() and not line.startswith(' ' * (len(line) - len(line.lstrip())) // 4 * 4):
                if len(line) - len(line.lstrip()) % 4 != 0:
                    issue = CodeError(
                        error_type="inconsistent_indentation",
                        severity="medium",
                        message="Inconsistent indentation (not multiple of 4)",
                        file_path=file_path,
                        line_number=line_num,
                        column_number=0,
                        error_code="INDENT_ERROR",
                        category="style",
                        confidence=0.9,
                        suggested_fix="Use 4 spaces for indentation",
                        explanation="Python code should use 4 spaces for indentation"
                    )
                    issues.append(issue)
        
        return issues

    def _check_python_logic_issues(self, code: str, file_path: str) -> List[CodeError]:
        """Check for Python logic issues."""
        issues = []
        lines = code.splitlines()
        
        for line_num, line in enumerate(lines, 1):
            # Check for potential infinite loops
            if re.search(r'while\s+True\s*:', line) and 'break' not in code:
                issue = CodeError(
                    error_type="potential_infinite_loop",
                    severity="medium",
                    message="Potential infinite loop detected",
                    file_path=file_path,
                    line_number=line_num,
                    column_number=line.find('while'),
                    error_code="INFINITE_LOOP",
                    category="logic",
                    confidence=0.6,
                    suggested_fix="Add break condition or loop termination",
                    explanation="While True loop without visible break condition"
                )
                issues.append(issue)
        
        return issues

    def suggest_fixes(self, errors: List[CodeError], code: str) -> Dict[str, str]:
        """Suggest fixes for detected errors.
        
        Args:
            errors: List of detected errors
            code: Original code
            
        Returns:
            Dictionary mapping error types to suggested fixes
        """
        fixes = {}
        
        for error in errors:
            if error.suggested_fix:
                fixes[error.error_type] = error.suggested_fix
        
        return fixes

    def auto_fix_errors(self, code: str, errors: List[CodeError]) -> str:
        """Automatically fix simple errors in code.
        
        Args:
            code: Original code
            errors: List of errors to fix
            
        Returns:
            Fixed code
        """
        fixed_code = code
        lines = fixed_code.splitlines()
        
        # Sort errors by line number (fix from bottom to top to preserve line numbers)
        sorted_errors = sorted(errors, key=lambda e: e.line_number, reverse=True)
        
        for error in sorted_errors:
            if error.error_type == "trailing_whitespace":
                line_idx = error.line_number - 1
                if line_idx < len(lines):
                    lines[line_idx] = lines[line_idx].rstrip()
            
            elif error.error_type == "js_var_usage":
                line_idx = error.line_number - 1
                if line_idx < len(lines):
                    lines[line_idx] = lines[line_idx].replace("var ", "let ")
            
            elif error.error_type == "py_bare_except":
                line_idx = error.line_number - 1
                if line_idx < len(lines):
                    lines[line_idx] = lines[line_idx].replace("except:", "except Exception:")
        
        return '\n'.join(lines)

    def add_custom_rule(self, pattern: ErrorPattern):
        """Add a custom error detection rule.
        
        Args:
            pattern: Custom error pattern
        """
        with self.lock:
            self.custom_rules.append(pattern)
            logger.info(f"Added custom rule: {pattern.pattern_id}")

    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error detection statistics."""
        with self.lock:
            if not self.detection_history:
                return {"total_detections": 0}
            
            total_detections = len(self.detection_history)
            total_issues = sum(r.total_issues for r in self.detection_history)
            total_critical = sum(r.critical_issues for r in self.detection_history)
            avg_time = sum(r.analysis_time for r in self.detection_history) / total_detections
            
            # Count error types
            error_types = {}
            for result in self.detection_history:
                for error in result.errors + result.warnings + result.suggestions:
                    error_types[error.error_type] = error_types.get(error.error_type, 0) + 1
            
            return {
                "total_detections": total_detections,
                "total_issues_found": total_issues,
                "critical_issues_found": total_critical,
                "average_detection_time": avg_time,
                "error_types": error_types,
                "custom_rules_count": len(self.custom_rules)
            }

    def clear_history(self):
        """Clear detection history."""
        with self.lock:
            self.detection_history.clear()
            logger.info("Cleared error detection history")
