#!/usr/bin/env python3
"""
Functional Capability Test Suite
Tests actual working capabilities with real code examples
"""

import os
import sys
import time
import traceback
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title: str):
    """Print formatted header"""
    print(f"\n{'='*80}")
    print(f"🧪 {title}")
    print(f"{'='*80}")

def print_test(test_name: str, success: bool, details: str = ""):
    """Print test result"""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"   {status} {test_name}")
    if details:
        print(f"      └─ {details}")

class FunctionalCapabilityTester:
    """Test actual working capabilities"""
    
    def __init__(self):
        self.workspace_dir = Path.cwd()
        self.enhanced_agent = None
        self.passed_tests = 0
        self.total_tests = 0
        
    def run_functional_tests(self):
        """Run all functional tests"""
        print_header("FUNCTIONAL CAPABILITY TEST SUITE")
        print("Testing actual working capabilities with real examples")
        
        start_time = time.time()
        
        # Initialize the enhanced agent
        if not self.initialize_enhanced_agent():
            print("❌ Failed to initialize enhanced agent. Cannot proceed with functional tests.")
            return
        
        # Run capability tests
        self.test_code_analysis_capability()
        self.test_performance_analysis_capability()
        self.test_code_generation_capability()
        self.test_refactoring_capability()
        self.test_completion_capability()
        self.test_learning_capability()
        self.test_multilanguage_capability()
        self.test_semantic_search_capability()
        self.test_rag_enhancement_capability()
        self.test_self_analysis_capability()
        
        # Print final results
        total_time = time.time() - start_time
        self.print_functional_summary(total_time)
    
    def initialize_enhanced_agent(self) -> bool:
        """Initialize the enhanced agent"""
        try:
            from core.model_manager import ModelManager
            from core.iterative_enhanced_agent import IterativeEnhancedAgent
            
            model_manager = ModelManager()
            self.enhanced_agent = IterativeEnhancedAgent(model_manager, self.workspace_dir)
            print("✅ Enhanced agent initialized successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize enhanced agent: {e}")
            return False
    
    def test_code_analysis_capability(self):
        """Test actual code analysis capability"""
        print_header("CODE ANALYSIS CAPABILITY")
        
        test_code = '''
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# This is inefficient recursive implementation
result = fibonacci(10)
print(result)
'''
        
        self.total_tests += 1
        try:
            analysis_result = self.enhanced_agent.analyze_code(test_code, "python")
            if analysis_result and isinstance(analysis_result, dict):
                issues_found = len(analysis_result.get('issues', []))
                print_test("Code Analysis", True, f"Analysis completed, {issues_found} issues found")
                self.passed_tests += 1
            else:
                print_test("Code Analysis", False, "No analysis result returned")
        except Exception as e:
            print_test("Code Analysis", False, f"Error: {e}")
    
    def test_performance_analysis_capability(self):
        """Test performance analysis capability"""
        print_header("PERFORMANCE ANALYSIS CAPABILITY")
        
        test_code = '''
def slow_function():
    result = []
    for i in range(1000):
        for j in range(1000):
            result.append(i * j)
    return result

data = slow_function()
'''
        
        self.total_tests += 1
        try:
            perf_result = self.enhanced_agent.analyze_performance(test_code, "python")
            if perf_result and isinstance(perf_result, dict):
                bottlenecks = len(perf_result.get('bottlenecks', []))
                print_test("Performance Analysis", True, f"Analysis completed, {bottlenecks} bottlenecks found")
                self.passed_tests += 1
            else:
                print_test("Performance Analysis", False, "No performance result returned")
        except Exception as e:
            print_test("Performance Analysis", False, f"Error: {e}")
    
    def test_code_generation_capability(self):
        """Test code generation capability"""
        print_header("CODE GENERATION CAPABILITY")
        
        prompt = "Create a Python function that calculates the factorial of a number using recursion"
        
        self.total_tests += 1
        try:
            generated_code = self.enhanced_agent.generate_code(prompt, "python")
            if generated_code and len(generated_code.strip()) > 50:
                lines = len(generated_code.strip().split('\n'))
                print_test("Code Generation", True, f"Generated {lines} lines of code")
                self.passed_tests += 1
            else:
                print_test("Code Generation", False, "No code generated or too short")
        except Exception as e:
            print_test("Code Generation", False, f"Error: {e}")
    
    def test_refactoring_capability(self):
        """Test refactoring capability"""
        print_header("REFACTORING CAPABILITY")
        
        test_code = '''
def calculate_area(length, width):
    area = length * width
    return area

def calculate_perimeter(length, width):
    perimeter = 2 * (length + width)
    return perimeter
'''
        
        self.total_tests += 1
        try:
            refactored_code = self.enhanced_agent.refactor_code(test_code, "python", ["performance", "readability"])
            if refactored_code and len(refactored_code.strip()) > 0:
                lines = len(refactored_code.strip().split('\n'))
                print_test("Code Refactoring", True, f"Refactored to {lines} lines")
                self.passed_tests += 1
            else:
                print_test("Code Refactoring", False, "No refactored code returned")
        except Exception as e:
            print_test("Code Refactoring", False, f"Error: {e}")
    
    def test_completion_capability(self):
        """Test code completion capability"""
        print_header("CODE COMPLETION CAPABILITY")
        
        partial_code = "import os\nos.path."
        
        self.total_tests += 1
        try:
            completions = self.enhanced_agent.complete_code(partial_code, "python")
            if completions and len(completions) > 0:
                print_test("Code Completion", True, f"Generated {len(completions)} completions")
                self.passed_tests += 1
            else:
                print_test("Code Completion", False, "No completions generated")
        except Exception as e:
            print_test("Code Completion", False, f"Error: {e}")
    
    def test_learning_capability(self):
        """Test learning system capability"""
        print_header("LEARNING SYSTEM CAPABILITY")
        
        self.total_tests += 1
        try:
            learning_metrics = self.enhanced_agent.get_learning_metrics()
            if learning_metrics and isinstance(learning_metrics, dict):
                interactions = learning_metrics.get('total_interactions', 0)
                print_test("Learning System", True, f"Learning metrics retrieved: {interactions} interactions")
                self.passed_tests += 1
            else:
                print_test("Learning System", False, "No learning metrics returned")
        except Exception as e:
            print_test("Learning System", False, f"Error: {e}")
    
    def test_multilanguage_capability(self):
        """Test multi-language support"""
        print_header("MULTI-LANGUAGE CAPABILITY")
        
        js_code = '''
function greet(name) {
    console.log("Hello, " + name);
}
greet("World");
'''
        
        self.total_tests += 1
        try:
            js_analysis = self.enhanced_agent.analyze_code(js_code, "javascript")
            if js_analysis and isinstance(js_analysis, dict):
                issues = len(js_analysis.get('issues', []))
                print_test("JavaScript Analysis", True, f"JavaScript analysis completed: {issues} issues found")
                self.passed_tests += 1
            else:
                print_test("JavaScript Analysis", False, "No JavaScript analysis result")
        except Exception as e:
            print_test("JavaScript Analysis", False, f"Error: {e}")
    
    def test_semantic_search_capability(self):
        """Test semantic search capability"""
        print_header("SEMANTIC SEARCH CAPABILITY")
        
        query = "function that calculates fibonacci"
        
        self.total_tests += 1
        try:
            search_results = self.enhanced_agent.search_code(query)
            if search_results and len(search_results) >= 0:
                print_test("Semantic Search", True, f"Search completed: {len(search_results)} results found")
                self.passed_tests += 1
            else:
                print_test("Semantic Search", False, "No search results returned")
        except Exception as e:
            print_test("Semantic Search", False, f"Error: {e}")
    
    def test_rag_enhancement_capability(self):
        """Test RAG enhancement capability"""
        print_header("RAG ENHANCEMENT CAPABILITY")
        
        query = "How to optimize Python code for better performance?"
        
        self.total_tests += 1
        try:
            rag_response = self.enhanced_agent.get_enhanced_response(query)
            if rag_response and len(rag_response.strip()) > 10:
                chars = len(rag_response.strip())
                print_test("RAG Enhancement", True, f"RAG response generated: {chars} characters")
                self.passed_tests += 1
            else:
                print_test("RAG Enhancement", False, "No RAG response generated")
        except Exception as e:
            print_test("RAG Enhancement", False, f"Error: {e}")
    
    def test_self_analysis_capability(self):
        """Test self-analysis capability"""
        print_header("SELF-ANALYSIS CAPABILITY")
        
        self.total_tests += 1
        try:
            self_analysis = self.enhanced_agent.perform_self_analysis()
            if self_analysis and isinstance(self_analysis, dict):
                confidence = self_analysis.get('confidence_score', 0)
                print_test("Self-Analysis", True, f"Self-analysis completed: {confidence:.2f} confidence")
                self.passed_tests += 1
            else:
                print_test("Self-Analysis", False, "No self-analysis result")
        except Exception as e:
            print_test("Self-Analysis", False, f"Error: {e}")
    
    def print_functional_summary(self, total_time: float):
        """Print functional test summary"""
        print_header("FUNCTIONAL TEST SUMMARY")
        
        percentage = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        print(f"⏱️  Total execution time: {total_time:.2f} seconds")
        print(f"🧪 Total functional tests: {self.total_tests}")
        print(f"✅ Tests passed: {self.passed_tests}")
        print(f"❌ Tests failed: {self.total_tests - self.passed_tests}")
        print(f"📊 Success rate: {percentage:.1f}%")
        
        if percentage >= 90:
            print("\n🎉 EXCELLENT! All major capabilities are working perfectly!")
        elif percentage >= 80:
            print("\n✅ GOOD! Most capabilities are working correctly.")
        elif percentage >= 60:
            print("\n⚠️  PARTIAL! Some capabilities need attention.")
        else:
            print("\n❌ CRITICAL! Major capabilities are not working.")
        
        print("=" * 80)

def main():
    """Main functional test execution"""
    tester = FunctionalCapabilityTester()
    tester.run_functional_tests()

if __name__ == "__main__":
    main()
