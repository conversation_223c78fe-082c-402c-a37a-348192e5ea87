#!/usr/bin/env python3
"""
Comprehensive test of enhanced AI coding assistant capabilities.
"""

import sys
import os
from pathlib import Path
import traceback

# Add the current directory to the path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_enhanced_capabilities():
    """Test the enhanced capabilities of the AI coding assistant."""
    print("Enhanced AI Coding Assistant - Capability Test")
    print("=" * 60)
    
    try:
        # Import required modules
        from models import ModelManager
        from conversation import ConversationManager
        from core.iterative_enhanced_agent import IterativeEnhancedAgent
        
        # Initialize components
        workspace_dir = Path.cwd()
        model_manager = ModelManager(
            provider="gemini",
            model_name="gemini-2.0-flash",
            temperature=0.7,
            max_tokens=4096
        )
        conversation_manager = ConversationManager(workspace_dir / "conversations")
        
        # Create enhanced agent
        enhanced_agent = IterativeEnhancedAgent(
            model_manager=model_manager,
            conversation_manager=conversation_manager,
            workspace_dir=workspace_dir
        )
        
        print("✓ Enhanced agent initialized successfully")
        
        # Test 1: Code Analysis
        print("\n1. Testing Code Analysis Capabilities...")
        test_code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

def process_data(data):
    result = ""
    for item in data:
        result += str(item) + " "
    return result
"""
        
        analysis_result = enhanced_agent.analyze_code(test_code, "python")
        print(f"   ✓ Code analysis completed: {len(analysis_result.get('issues', []))} issues found")
        
        # Test 2: Performance Analysis
        print("\n2. Testing Performance Analysis...")
        perf_analysis = enhanced_agent.analyze_performance(test_code, "python")
        print(f"   ✓ Performance analysis completed: {len(perf_analysis.bottlenecks)} bottlenecks found")
        
        # Test 3: Code Generation
        print("\n3. Testing Code Generation...")
        generated_code = enhanced_agent.generate_code(
            "Create a function that calculates the factorial of a number using memoization",
            "python"
        )
        print(f"   ✓ Code generation completed: {len(generated_code.splitlines())} lines generated")
        
        # Test 4: Intelligent Refactoring
        print("\n4. Testing Intelligent Refactoring...")
        refactored_code = enhanced_agent.refactor_code(test_code, "python", ["performance", "readability"])
        print(f"   ✓ Code refactoring completed: {len(refactored_code.splitlines())} lines refactored")
        
        # Test 5: Context-Aware Completion
        print("\n5. Testing Context-Aware Completion...")
        completion = enhanced_agent.complete_code("def binary_search(arr, target):", "python")
        print(f"   ✓ Code completion generated: {len(completion.splitlines())} lines")
        
        # Test 6: Learning System
        print("\n6. Testing Learning System...")
        learning_metrics = enhanced_agent.get_learning_metrics()
        print(f"   ✓ Learning metrics retrieved: {learning_metrics.total_interactions} interactions")
        
        # Test 7: Multi-language Support
        print("\n7. Testing Multi-language Support...")
        js_code = """
function bubbleSort(arr) {
    for (let i = 0; i < arr.length; i++) {
        for (let j = 0; j < arr.length - i - 1; j++) {
            if (arr[j] > arr[j + 1]) {
                let temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
            }
        }
    }
    return arr;
}
"""
        js_analysis = enhanced_agent.analyze_code(js_code, "javascript")
        print(f"   ✓ JavaScript analysis completed: {len(js_analysis.get('issues', []))} issues found")
        
        # Test 8: Semantic Indexing
        print("\n8. Testing Semantic Indexing...")
        enhanced_agent.index_codebase(workspace_dir)
        search_results = enhanced_agent.search_code("fibonacci function")
        print(f"   ✓ Semantic search completed: {len(search_results)} results found")
        
        # Test 9: RAG Enhancement
        print("\n9. Testing RAG Enhancement...")
        rag_response = enhanced_agent.get_enhanced_response(
            "How can I optimize recursive functions?",
            {"language": "python", "context": "performance"}
        )
        print(f"   ✓ RAG-enhanced response generated: {len(rag_response)} characters")
        
        # Test 10: Self-Analysis
        print("\n10. Testing Self-Analysis...")
        self_analysis = enhanced_agent.perform_self_analysis()
        print(f"   ✓ Self-analysis completed: {self_analysis.get('confidence_score', 0):.2f} confidence")
        
        print("\n" + "=" * 60)
        print("🎉 All enhanced capabilities tested successfully!")
        print("The AI coding assistant is ready for advanced development tasks.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error testing enhanced capabilities: {e}")
        print("Full traceback:")
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    success = test_enhanced_capabilities()
    
    if success:
        print("\n🚀 Enhanced AI Coding Assistant is fully operational!")
        print("Ready to assist with complex coding tasks, learning, and optimization.")
    else:
        print("\n⚠️  Some enhanced capabilities may not be fully functional.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
