"""
Intelligent Refactoring for the AI Code Assistant.

This module provides smart refactoring suggestions and automated code improvements
based on analysis of code patterns, maintainability metrics, and best practices.
"""

import time
import logging
import threading
import ast
import re
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class RefactoringOpportunity:
    """Represents a refactoring opportunity."""
    type: str  # extract_method, rename_variable, simplify_condition, etc.
    description: str
    location: Dict[str, int]  # file, line, column
    severity: str  # low, medium, high, critical
    confidence: float
    estimated_effort: str  # trivial, easy, medium, hard
    benefits: List[str]
    risks: List[str]
    suggested_code: Optional[str]

@dataclass
class RefactoringResult:
    """Result of applying refactoring."""
    original_code: str
    refactored_code: str
    applied_refactorings: List[RefactoringOpportunity]
    improvements: Dict[str, float]
    warnings: List[str]
    success: bool

class IntelligentRefactoring:
    """Intelligent refactoring system with automated suggestions."""

    def __init__(self, workspace_dir: Path, model_manager=None):
        """Initialize the intelligent refactoring system.
        
        Args:
            workspace_dir: The workspace directory
            model_manager: Optional model manager for AI-assisted refactoring
        """
        self.workspace_dir = workspace_dir
        self.model_manager = model_manager
        self.refactoring_patterns: Dict[str, Any] = {}
        self.refactoring_history: List[RefactoringResult] = []
        self.lock = threading.RLock()
        
        # Initialize refactoring patterns
        self._initialize_refactoring_patterns()

    def _initialize_refactoring_patterns(self):
        """Initialize common refactoring patterns."""
        self.refactoring_patterns = {
            "extract_method": {
                "description": "Extract repeated code into a method",
                "pattern": r"(.{3,})\n.*\1",  # Simplified duplicate detection
                "benefits": ["Reduces code duplication", "Improves maintainability"],
                "effort": "easy"
            },
            "rename_variable": {
                "description": "Rename variables to be more descriptive",
                "pattern": r"\b[a-z]{1,2}\b",  # Single/double letter variables
                "benefits": ["Improves code readability", "Better self-documentation"],
                "effort": "trivial"
            },
            "simplify_condition": {
                "description": "Simplify complex conditional expressions",
                "pattern": r"if.*and.*and.*:",  # Complex conditions
                "benefits": ["Improves readability", "Reduces cognitive complexity"],
                "effort": "easy"
            },
            "extract_constant": {
                "description": "Extract magic numbers into named constants",
                "pattern": r"\b\d{2,}\b",  # Numbers with 2+ digits
                "benefits": ["Improves maintainability", "Reduces magic numbers"],
                "effort": "trivial"
            },
            "decompose_function": {
                "description": "Break down large functions into smaller ones",
                "pattern": None,  # Detected by line count
                "benefits": ["Improves testability", "Reduces complexity"],
                "effort": "medium"
            }
        }

    def analyze_refactoring_opportunities(self, code: str, language: str = "python") -> List[RefactoringOpportunity]:
        """Analyze code for refactoring opportunities.
        
        Args:
            code: The code to analyze
            language: Programming language
            
        Returns:
            List of refactoring opportunities
        """
        with self.lock:
            logger.info(f"Analyzing {language} code for refactoring opportunities")
            
            opportunities = []
            
            if language == "python":
                opportunities.extend(self._analyze_python_refactoring(code))
            elif language in ["javascript", "typescript"]:
                opportunities.extend(self._analyze_javascript_refactoring(code))
            else:
                opportunities.extend(self._analyze_generic_refactoring(code))
            
            # Sort by severity and confidence
            opportunities.sort(key=lambda x: (
                {"critical": 4, "high": 3, "medium": 2, "low": 1}[x.severity],
                x.confidence
            ), reverse=True)
            
            logger.info(f"Found {len(opportunities)} refactoring opportunities")
            return opportunities

    def _analyze_python_refactoring(self, code: str) -> List[RefactoringOpportunity]:
        """Analyze Python code for refactoring opportunities."""
        opportunities = []
        
        try:
            tree = ast.parse(code)
            lines = code.splitlines()
            
            # Analyze functions
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    opportunities.extend(self._analyze_function_refactoring(node, lines))
                elif isinstance(node, ast.ClassDef):
                    opportunities.extend(self._analyze_class_refactoring(node, lines))
            
            # Analyze code patterns
            opportunities.extend(self._analyze_code_patterns(code, "python"))
            
        except SyntaxError as e:
            logger.warning(f"Syntax error in Python code: {e}")
        
        return opportunities

    def _analyze_function_refactoring(self, node: ast.FunctionDef, lines: List[str]) -> List[RefactoringOpportunity]:
        """Analyze function for refactoring opportunities."""
        opportunities = []
        
        # Check function length
        if hasattr(node, 'end_lineno'):
            function_length = node.end_lineno - node.lineno + 1
            if function_length > 50:
                opportunities.append(RefactoringOpportunity(
                    type="decompose_function",
                    description=f"Function '{node.name}' is too long ({function_length} lines)",
                    location={"line": node.lineno, "column": node.col_offset},
                    severity="medium",
                    confidence=0.8,
                    estimated_effort="medium",
                    benefits=["Improves readability", "Easier testing", "Better maintainability"],
                    risks=["May increase complexity if over-decomposed"],
                    suggested_code=None
                ))
        
        # Check parameter count
        if len(node.args.args) > 5:
            opportunities.append(RefactoringOpportunity(
                type="reduce_parameters",
                description=f"Function '{node.name}' has too many parameters ({len(node.args.args)})",
                location={"line": node.lineno, "column": node.col_offset},
                severity="medium",
                confidence=0.7,
                estimated_effort="medium",
                benefits=["Improves function interface", "Easier to use"],
                risks=["May require parameter object pattern"],
                suggested_code=None
            ))
        
        # Check for missing docstring
        if not ast.get_docstring(node):
            opportunities.append(RefactoringOpportunity(
                type="add_docstring",
                description=f"Function '{node.name}' lacks documentation",
                location={"line": node.lineno, "column": node.col_offset},
                severity="low",
                confidence=0.9,
                estimated_effort="trivial",
                benefits=["Improves code documentation", "Better maintainability"],
                risks=[],
                suggested_code=self._generate_docstring_suggestion(node)
            ))
        
        return opportunities

    def _analyze_class_refactoring(self, node: ast.ClassDef, lines: List[str]) -> List[RefactoringOpportunity]:
        """Analyze class for refactoring opportunities."""
        opportunities = []
        
        # Check for missing docstring
        if not ast.get_docstring(node):
            opportunities.append(RefactoringOpportunity(
                type="add_class_docstring",
                description=f"Class '{node.name}' lacks documentation",
                location={"line": node.lineno, "column": node.col_offset},
                severity="low",
                confidence=0.9,
                estimated_effort="trivial",
                benefits=["Improves code documentation"],
                risks=[],
                suggested_code=f'    """{node.name} class description."""'
            ))
        
        # Check method count
        methods = [n for n in node.body if isinstance(n, ast.FunctionDef)]
        if len(methods) > 20:
            opportunities.append(RefactoringOpportunity(
                type="split_class",
                description=f"Class '{node.name}' has too many methods ({len(methods)})",
                location={"line": node.lineno, "column": node.col_offset},
                severity="high",
                confidence=0.6,
                estimated_effort="hard",
                benefits=["Better separation of concerns", "Improved maintainability"],
                risks=["May break existing dependencies"],
                suggested_code=None
            ))
        
        return opportunities

    def _analyze_code_patterns(self, code: str, language: str) -> List[RefactoringOpportunity]:
        """Analyze code for pattern-based refactoring opportunities."""
        opportunities = []
        lines = code.splitlines()
        
        # Check for magic numbers
        for i, line in enumerate(lines):
            magic_numbers = re.findall(r'\b\d{2,}\b', line)
            for number in magic_numbers:
                if number not in ["100", "200", "404", "500"]:  # Common HTTP codes
                    opportunities.append(RefactoringOpportunity(
                        type="extract_constant",
                        description=f"Magic number '{number}' should be extracted to a constant",
                        location={"line": i + 1, "column": line.find(number)},
                        severity="low",
                        confidence=0.6,
                        estimated_effort="trivial",
                        benefits=["Improves maintainability", "Reduces magic numbers"],
                        risks=[],
                        suggested_code=f"CONSTANT_NAME = {number}"
                    ))
        
        # Check for long lines
        for i, line in enumerate(lines):
            if len(line) > 120:
                opportunities.append(RefactoringOpportunity(
                    type="break_long_line",
                    description=f"Line {i + 1} is too long ({len(line)} characters)",
                    location={"line": i + 1, "column": 0},
                    severity="low",
                    confidence=0.8,
                    estimated_effort="trivial",
                    benefits=["Improves readability"],
                    risks=[],
                    suggested_code=None
                ))
        
        # Check for code duplication
        opportunities.extend(self._detect_code_duplication(lines))
        
        return opportunities

    def _analyze_javascript_refactoring(self, code: str) -> List[RefactoringOpportunity]:
        """Analyze JavaScript code for refactoring opportunities."""
        opportunities = []
        lines = code.splitlines()
        
        # Check for var usage
        for i, line in enumerate(lines):
            if re.search(r'\bvar\s+', line):
                opportunities.append(RefactoringOpportunity(
                    type="replace_var",
                    description="Replace 'var' with 'let' or 'const'",
                    location={"line": i + 1, "column": line.find("var")},
                    severity="medium",
                    confidence=0.9,
                    estimated_effort="trivial",
                    benefits=["Better scoping", "Prevents hoisting issues"],
                    risks=[],
                    suggested_code=line.replace("var ", "let ")
                ))
        
        # Check for function expressions that could be arrow functions
        for i, line in enumerate(lines):
            if re.search(r'function\s*\([^)]*\)\s*{', line):
                opportunities.append(RefactoringOpportunity(
                    type="use_arrow_function",
                    description="Consider using arrow function syntax",
                    location={"line": i + 1, "column": line.find("function")},
                    severity="low",
                    confidence=0.7,
                    estimated_effort="trivial",
                    benefits=["More concise syntax", "Lexical this binding"],
                    risks=["Different 'this' behavior"],
                    suggested_code=None
                ))
        
        return opportunities

    def _analyze_generic_refactoring(self, code: str) -> List[RefactoringOpportunity]:
        """Analyze code for generic refactoring opportunities."""
        opportunities = []
        lines = code.splitlines()
        
        # Check for long lines
        for i, line in enumerate(lines):
            if len(line) > 120:
                opportunities.append(RefactoringOpportunity(
                    type="break_long_line",
                    description=f"Line {i + 1} is too long ({len(line)} characters)",
                    location={"line": i + 1, "column": 0},
                    severity="low",
                    confidence=0.8,
                    estimated_effort="trivial",
                    benefits=["Improves readability"],
                    risks=[],
                    suggested_code=None
                ))
        
        return opportunities

    def _detect_code_duplication(self, lines: List[str]) -> List[RefactoringOpportunity]:
        """Detect code duplication."""
        opportunities = []
        
        # Simple duplication detection
        line_counts = {}
        for i, line in enumerate(lines):
            stripped = line.strip()
            if len(stripped) > 10 and not stripped.startswith("#"):  # Ignore short lines and comments
                if stripped in line_counts:
                    line_counts[stripped].append(i + 1)
                else:
                    line_counts[stripped] = [i + 1]
        
        # Find duplicates
        for line_content, line_numbers in line_counts.items():
            if len(line_numbers) > 1:
                opportunities.append(RefactoringOpportunity(
                    type="extract_method",
                    description=f"Duplicate code found on lines {', '.join(map(str, line_numbers))}",
                    location={"line": line_numbers[0], "column": 0},
                    severity="medium",
                    confidence=0.6,
                    estimated_effort="easy",
                    benefits=["Reduces code duplication", "Improves maintainability"],
                    risks=["May over-abstract if not carefully done"],
                    suggested_code=None
                ))
        
        return opportunities

    def _generate_docstring_suggestion(self, node: ast.FunctionDef) -> str:
        """Generate a docstring suggestion for a function."""
        args = [arg.arg for arg in node.args.args]
        
        docstring = f'    """{node.name} function.\n\n'
        
        if args:
            docstring += "    Args:\n"
            for arg in args:
                docstring += f"        {arg}: Description of {arg}\n"
            docstring += "\n"
        
        docstring += "    Returns:\n"
        docstring += "        Description of return value\n"
        docstring += '    """'
        
        return docstring

    def apply_refactoring(self, code: str, opportunities: List[RefactoringOpportunity], 
                         language: str = "python") -> RefactoringResult:
        """Apply selected refactoring opportunities.
        
        Args:
            code: Original code
            opportunities: Refactoring opportunities to apply
            language: Programming language
            
        Returns:
            Refactoring result
        """
        with self.lock:
            logger.info(f"Applying {len(opportunities)} refactoring opportunities")
            
            refactored_code = code
            applied_refactorings = []
            warnings = []
            
            # Sort opportunities by line number (apply from bottom to top to preserve line numbers)
            sorted_opportunities = sorted(opportunities, key=lambda x: x.location.get("line", 0), reverse=True)
            
            for opportunity in sorted_opportunities:
                try:
                    if opportunity.suggested_code:
                        # Apply the suggested code
                        refactored_code = self._apply_code_suggestion(
                            refactored_code, opportunity, language
                        )
                        applied_refactorings.append(opportunity)
                    else:
                        warnings.append(f"No automatic fix available for {opportunity.type}")
                
                except Exception as e:
                    warning_msg = f"Failed to apply {opportunity.type}: {e}"
                    warnings.append(warning_msg)
                    logger.warning(warning_msg)
            
            # Calculate improvements
            improvements = self._calculate_improvements(code, refactored_code, language)
            
            result = RefactoringResult(
                original_code=code,
                refactored_code=refactored_code,
                applied_refactorings=applied_refactorings,
                improvements=improvements,
                warnings=warnings,
                success=len(applied_refactorings) > 0
            )
            
            # Store in history
            self.refactoring_history.append(result)
            
            logger.info(f"Applied {len(applied_refactorings)} refactorings successfully")
            return result

    def _apply_code_suggestion(self, code: str, opportunity: RefactoringOpportunity, language: str) -> str:
        """Apply a code suggestion."""
        lines = code.splitlines()
        line_number = opportunity.location.get("line", 1) - 1  # Convert to 0-based
        
        if opportunity.type == "add_docstring":
            # Insert docstring after function definition
            if line_number < len(lines):
                lines.insert(line_number + 1, opportunity.suggested_code)
        
        elif opportunity.type == "add_class_docstring":
            # Insert docstring after class definition
            if line_number < len(lines):
                lines.insert(line_number + 1, opportunity.suggested_code)
        
        elif opportunity.type == "replace_var" and language == "javascript":
            # Replace var with let/const
            if line_number < len(lines):
                lines[line_number] = opportunity.suggested_code
        
        elif opportunity.type == "extract_constant":
            # Add constant at the beginning of the file
            lines.insert(0, opportunity.suggested_code)
            lines.insert(1, "")  # Add blank line
        
        return '\n'.join(lines)

    def _calculate_improvements(self, original: str, refactored: str, language: str) -> Dict[str, float]:
        """Calculate improvements from refactoring."""
        improvements = {}
        
        # Line count improvement
        original_lines = len(original.splitlines())
        refactored_lines = len(refactored.splitlines())
        improvements["line_count_change"] = (refactored_lines - original_lines) / max(original_lines, 1)
        
        # Documentation improvement (count docstrings)
        original_docs = original.count('"""') + original.count("'''")
        refactored_docs = refactored.count('"""') + refactored.count("'''")
        improvements["documentation_improvement"] = (refactored_docs - original_docs) / max(original_docs, 1)
        
        # Complexity improvement (rough estimate)
        original_complexity = self._estimate_complexity(original)
        refactored_complexity = self._estimate_complexity(refactored)
        improvements["complexity_reduction"] = (original_complexity - refactored_complexity) / max(original_complexity, 1)
        
        return improvements

    def _estimate_complexity(self, code: str) -> float:
        """Estimate code complexity."""
        # Simple complexity estimation
        complexity_indicators = ["if", "for", "while", "try", "except", "elif", "else"]
        complexity = sum(code.lower().count(indicator) for indicator in complexity_indicators)
        return complexity / max(len(code.splitlines()), 1)

    def get_refactoring_statistics(self) -> Dict[str, Any]:
        """Get refactoring statistics."""
        with self.lock:
            if not self.refactoring_history:
                return {"total_refactorings": 0}
            
            total_refactorings = len(self.refactoring_history)
            successful_refactorings = sum(1 for r in self.refactoring_history if r.success)
            
            # Count refactoring types
            refactoring_types = {}
            for result in self.refactoring_history:
                for refactoring in result.applied_refactorings:
                    refactoring_types[refactoring.type] = refactoring_types.get(refactoring.type, 0) + 1
            
            return {
                "total_refactorings": total_refactorings,
                "successful_refactorings": successful_refactorings,
                "success_rate": successful_refactorings / total_refactorings,
                "refactoring_types": refactoring_types,
                "average_improvements": self._calculate_average_improvements()
            }

    def _calculate_average_improvements(self) -> Dict[str, float]:
        """Calculate average improvements across all refactorings."""
        if not self.refactoring_history:
            return {}
        
        improvements = {}
        for result in self.refactoring_history:
            for key, value in result.improvements.items():
                if key not in improvements:
                    improvements[key] = []
                improvements[key].append(value)
        
        return {key: sum(values) / len(values) for key, values in improvements.items()}

    def refactor_code(self, code: str, language: str = "python", focus_areas: List[str] = None) -> str:
        """Refactor code with specified focus areas.

        Args:
            code: The code to refactor
            language: Programming language
            focus_areas: Areas to focus on (e.g., ["performance", "readability"])

        Returns:
            Refactored code
        """
        try:
            # Analyze refactoring opportunities
            opportunities = self.analyze_refactoring_opportunities(code, language)

            # Filter opportunities based on focus areas if specified
            if focus_areas:
                filtered_opportunities = []
                for opportunity in opportunities:
                    if any(focus in opportunity.type.lower() or focus in opportunity.description.lower()
                          for focus in focus_areas):
                        filtered_opportunities.append(opportunity)
                opportunities = filtered_opportunities

            # Apply refactoring
            if opportunities:
                result = self.apply_refactoring(code, opportunities, language)
                return result.refactored_code if result.success else code
            else:
                logger.info("No refactoring opportunities found")
                return code

        except Exception as e:
            logger.error(f"Error in refactor_code: {e}")
            return code

    def clear_history(self):
        """Clear refactoring history."""
        with self.lock:
            self.refactoring_history.clear()
            logger.info("Cleared refactoring history")
