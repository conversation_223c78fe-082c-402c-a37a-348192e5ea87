"""
Adaptive Controller for the AI Code Assistant.

This module controls adaptation based on situational context, learning from
past experiences and adjusting strategies dynamically.
"""

import time
import json
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import numpy as np

logger = logging.getLogger(__name__)

class AdaptationStrategy(Enum):
    """Available adaptation strategies."""
    CONSERVATIVE = "conservative"
    AGGRESSIVE = "aggressive"
    BALANCED = "balanced"
    EXPERIMENTAL = "experimental"
    LEARNING = "learning"

@dataclass
class ContextualSituation:
    """Represents a contextual situation for adaptation."""
    task_type: str
    language: str
    complexity_level: float
    error_history: List[str]
    performance_history: List[float]
    success_rate: float
    user_preferences: Dict[str, Any]
    environmental_factors: Dict[str, Any]

@dataclass
class AdaptationDecision:
    """Represents an adaptation decision."""
    strategy: AdaptationStrategy
    confidence: float
    reasoning: List[str]
    adjustments: Dict[str, Any]
    expected_outcome: Dict[str, Any]
    fallback_options: List[str]

class AdaptiveController:
    """Controls adaptation based on situational context and learning."""

    def __init__(self, model_manager, workspace_dir: Path):
        """Initialize the adaptive controller.
        
        Args:
            model_manager: The model manager for AI interactions
            workspace_dir: The workspace directory
        """
        self.model_manager = model_manager
        self.workspace_dir = workspace_dir
        self.adaptation_history: List[Dict[str, Any]] = []
        self.situation_patterns: Dict[str, Dict[str, Any]] = {}
        self.strategy_effectiveness: Dict[str, float] = {}
        self.learning_rate = 0.1
        self.lock = threading.RLock()
        
        # Initialize strategy effectiveness
        for strategy in AdaptationStrategy:
            self.strategy_effectiveness[strategy.value] = 0.5
        
        # Context analysis weights
        self.context_weights = {
            "task_complexity": 0.25,
            "error_frequency": 0.20,
            "performance_trend": 0.20,
            "success_rate": 0.15,
            "user_preference": 0.10,
            "environmental": 0.10,
        }

    def analyze_situation_and_adapt(self, situation: ContextualSituation) -> AdaptationDecision:
        """Analyze the current situation and determine adaptation strategy.
        
        Args:
            situation: The current contextual situation
            
        Returns:
            Adaptation decision with strategy and adjustments
        """
        with self.lock:
            logger.info(f"Analyzing situation for task: {situation.task_type}")
            
            # Analyze context
            context_analysis = self._analyze_context(situation)
            
            # Determine optimal strategy
            strategy = self._select_adaptation_strategy(situation, context_analysis)
            
            # Generate specific adjustments
            adjustments = self._generate_adjustments(situation, strategy, context_analysis)
            
            # Calculate confidence
            confidence = self._calculate_adaptation_confidence(situation, strategy, context_analysis)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(situation, strategy, context_analysis)
            
            # Predict expected outcome
            expected_outcome = self._predict_outcome(situation, strategy, adjustments)
            
            # Determine fallback options
            fallback_options = self._determine_fallback_options(situation, strategy)
            
            decision = AdaptationDecision(
                strategy=strategy,
                confidence=confidence,
                reasoning=reasoning,
                adjustments=adjustments,
                expected_outcome=expected_outcome,
                fallback_options=fallback_options
            )
            
            # Record decision for learning
            self._record_adaptation_decision(situation, decision)
            
            return decision

    def _analyze_context(self, situation: ContextualSituation) -> Dict[str, Any]:
        """Analyze the contextual situation.
        
        Args:
            situation: The contextual situation
            
        Returns:
            Context analysis results
        """
        analysis = {
            "complexity_score": self._assess_complexity(situation),
            "error_severity": self._assess_error_severity(situation),
            "performance_trend": self._assess_performance_trend(situation),
            "stability_score": self._assess_stability(situation),
            "learning_potential": self._assess_learning_potential(situation),
            "risk_level": self._assess_risk_level(situation),
        }
        
        # Calculate overall context score
        analysis["overall_score"] = self._calculate_overall_context_score(analysis)
        
        return analysis

    def _select_adaptation_strategy(self, situation: ContextualSituation, 
                                   context_analysis: Dict[str, Any]) -> AdaptationStrategy:
        """Select the optimal adaptation strategy.
        
        Args:
            situation: The contextual situation
            context_analysis: Context analysis results
            
        Returns:
            Selected adaptation strategy
        """
        strategy_scores = {}
        
        for strategy in AdaptationStrategy:
            score = self._score_strategy_for_situation(strategy, situation, context_analysis)
            strategy_scores[strategy] = score
        
        # Select strategy with highest score
        best_strategy = max(strategy_scores, key=strategy_scores.get)
        
        logger.info(f"Selected strategy: {best_strategy.value} (score: {strategy_scores[best_strategy]:.3f})")
        
        return best_strategy

    def _score_strategy_for_situation(self, strategy: AdaptationStrategy, 
                                     situation: ContextualSituation,
                                     context_analysis: Dict[str, Any]) -> float:
        """Score a strategy for the given situation.
        
        Args:
            strategy: The adaptation strategy
            situation: The contextual situation
            context_analysis: Context analysis results
            
        Returns:
            Strategy score (0-1)
        """
        base_score = self.strategy_effectiveness.get(strategy.value, 0.5)
        
        # Adjust based on context
        if strategy == AdaptationStrategy.CONSERVATIVE:
            # Conservative works well for high-risk, low-complexity situations
            score = base_score + (1 - context_analysis["risk_level"]) * 0.3
            score += (1 - situation.complexity_level) * 0.2
            
        elif strategy == AdaptationStrategy.AGGRESSIVE:
            # Aggressive works well for low-risk, high-performance situations
            score = base_score + (1 - context_analysis["risk_level"]) * 0.2
            score += context_analysis["performance_trend"] * 0.3
            
        elif strategy == AdaptationStrategy.BALANCED:
            # Balanced works well for moderate situations
            score = base_score + (1 - abs(context_analysis["overall_score"] - 0.5)) * 0.3
            
        elif strategy == AdaptationStrategy.EXPERIMENTAL:
            # Experimental works well for high learning potential
            score = base_score + context_analysis["learning_potential"] * 0.4
            score -= context_analysis["risk_level"] * 0.2
            
        elif strategy == AdaptationStrategy.LEARNING:
            # Learning works well for situations with patterns
            score = base_score + context_analysis["learning_potential"] * 0.3
            score += (1 - context_analysis["stability_score"]) * 0.2
        
        return max(0.0, min(1.0, score))

    def _generate_adjustments(self, situation: ContextualSituation, 
                             strategy: AdaptationStrategy,
                             context_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate specific adjustments based on strategy.
        
        Args:
            situation: The contextual situation
            strategy: The selected strategy
            context_analysis: Context analysis results
            
        Returns:
            Dictionary of adjustments
        """
        adjustments = {
            "timeout_multiplier": 1.0,
            "retry_attempts": 3,
            "optimization_level": "medium",
            "error_tolerance": 0.1,
            "learning_rate": self.learning_rate,
            "monitoring_frequency": 1.0,
            "fallback_threshold": 0.3,
        }
        
        if strategy == AdaptationStrategy.CONSERVATIVE:
            adjustments.update({
                "timeout_multiplier": 2.0,
                "retry_attempts": 5,
                "optimization_level": "low",
                "error_tolerance": 0.05,
                "monitoring_frequency": 2.0,
            })
            
        elif strategy == AdaptationStrategy.AGGRESSIVE:
            adjustments.update({
                "timeout_multiplier": 0.5,
                "retry_attempts": 1,
                "optimization_level": "high",
                "error_tolerance": 0.2,
                "monitoring_frequency": 0.5,
            })
            
        elif strategy == AdaptationStrategy.EXPERIMENTAL:
            adjustments.update({
                "timeout_multiplier": 1.5,
                "retry_attempts": 2,
                "optimization_level": "experimental",
                "error_tolerance": 0.15,
                "learning_rate": self.learning_rate * 2,
            })
            
        elif strategy == AdaptationStrategy.LEARNING:
            adjustments.update({
                "learning_rate": self.learning_rate * 1.5,
                "monitoring_frequency": 1.5,
                "fallback_threshold": 0.2,
            })
        
        # Adjust based on context
        if context_analysis["complexity_score"] > 0.8:
            adjustments["timeout_multiplier"] *= 1.5
            adjustments["retry_attempts"] += 2
        
        if context_analysis["error_severity"] > 0.7:
            adjustments["error_tolerance"] *= 0.5
            adjustments["retry_attempts"] += 1
        
        return adjustments

    def _calculate_adaptation_confidence(self, situation: ContextualSituation,
                                        strategy: AdaptationStrategy,
                                        context_analysis: Dict[str, Any]) -> float:
        """Calculate confidence in the adaptation decision.
        
        Args:
            situation: The contextual situation
            strategy: The selected strategy
            context_analysis: Context analysis results
            
        Returns:
            Confidence score (0-1)
        """
        base_confidence = self.strategy_effectiveness.get(strategy.value, 0.5)
        
        # Adjust based on situation familiarity
        situation_key = f"{situation.task_type}_{situation.language}"
        if situation_key in self.situation_patterns:
            pattern_match = self._calculate_pattern_match(situation, self.situation_patterns[situation_key])
            base_confidence += pattern_match * 0.3
        
        # Adjust based on context clarity
        context_clarity = 1.0 - np.std(list(context_analysis.values()))
        base_confidence += context_clarity * 0.2
        
        # Adjust based on success rate
        base_confidence += situation.success_rate * 0.2
        
        return max(0.0, min(1.0, base_confidence))

    def _generate_reasoning(self, situation: ContextualSituation,
                           strategy: AdaptationStrategy,
                           context_analysis: Dict[str, Any]) -> List[str]:
        """Generate reasoning for the adaptation decision.
        
        Args:
            situation: The contextual situation
            strategy: The selected strategy
            context_analysis: Context analysis results
            
        Returns:
            List of reasoning statements
        """
        reasoning = []
        
        # Strategy selection reasoning
        reasoning.append(f"Selected {strategy.value} strategy based on context analysis")
        
        # Context-based reasoning
        if context_analysis["complexity_score"] > 0.7:
            reasoning.append("High complexity detected, requiring careful approach")
        
        if context_analysis["error_severity"] > 0.6:
            reasoning.append("Significant errors in history, prioritizing stability")
        
        if context_analysis["performance_trend"] > 0.7:
            reasoning.append("Positive performance trend, can be more aggressive")
        
        if context_analysis["learning_potential"] > 0.7:
            reasoning.append("High learning potential, worth experimental approaches")
        
        # Success rate reasoning
        if situation.success_rate < 0.5:
            reasoning.append("Low success rate requires conservative adjustments")
        elif situation.success_rate > 0.8:
            reasoning.append("High success rate allows for optimization")
        
        return reasoning

    def _predict_outcome(self, situation: ContextualSituation,
                        strategy: AdaptationStrategy,
                        adjustments: Dict[str, Any]) -> Dict[str, Any]:
        """Predict expected outcome of the adaptation.
        
        Args:
            situation: The contextual situation
            strategy: The selected strategy
            adjustments: The adjustments to be made
            
        Returns:
            Predicted outcome
        """
        base_success_rate = situation.success_rate
        strategy_effectiveness = self.strategy_effectiveness.get(strategy.value, 0.5)
        
        # Predict success rate improvement
        predicted_success_rate = min(1.0, base_success_rate + (strategy_effectiveness - 0.5) * 0.3)
        
        # Predict performance improvement
        performance_baseline = np.mean(situation.performance_history) if situation.performance_history else 0.5
        predicted_performance = min(1.0, performance_baseline + (strategy_effectiveness - 0.5) * 0.2)
        
        # Predict execution time
        timeout_factor = adjustments.get("timeout_multiplier", 1.0)
        predicted_execution_time = f"{timeout_factor:.1f}x baseline"
        
        return {
            "predicted_success_rate": predicted_success_rate,
            "predicted_performance": predicted_performance,
            "predicted_execution_time": predicted_execution_time,
            "confidence_interval": [predicted_success_rate - 0.1, predicted_success_rate + 0.1],
        }

    def _determine_fallback_options(self, situation: ContextualSituation,
                                   strategy: AdaptationStrategy) -> List[str]:
        """Determine fallback options if the strategy fails.
        
        Args:
            situation: The contextual situation
            strategy: The selected strategy
            
        Returns:
            List of fallback options
        """
        fallbacks = []
        
        # Always have conservative as fallback for aggressive strategies
        if strategy == AdaptationStrategy.AGGRESSIVE:
            fallbacks.append("Switch to conservative strategy")
        
        # Balanced as fallback for experimental
        if strategy == AdaptationStrategy.EXPERIMENTAL:
            fallbacks.append("Switch to balanced strategy")
        
        # Reduce complexity
        if situation.complexity_level > 0.5:
            fallbacks.append("Simplify the approach")
        
        # Increase timeout
        fallbacks.append("Increase timeout and retry")
        
        # Manual intervention
        if situation.success_rate < 0.3:
            fallbacks.append("Request manual intervention")
        
        return fallbacks

    def learn_from_outcome(self, decision: AdaptationDecision, actual_outcome: Dict[str, Any]):
        """Learn from the actual outcome of an adaptation decision.
        
        Args:
            decision: The adaptation decision that was made
            actual_outcome: The actual outcome that occurred
        """
        with self.lock:
            # Calculate outcome quality
            predicted_success = decision.expected_outcome.get("predicted_success_rate", 0.5)
            actual_success = actual_outcome.get("success_rate", 0.0)
            
            outcome_quality = 1.0 - abs(predicted_success - actual_success)
            
            # Update strategy effectiveness
            strategy_key = decision.strategy.value
            current_effectiveness = self.strategy_effectiveness[strategy_key]
            
            # Use learning rate to update effectiveness
            new_effectiveness = current_effectiveness + self.learning_rate * (outcome_quality - current_effectiveness)
            self.strategy_effectiveness[strategy_key] = max(0.0, min(1.0, new_effectiveness))
            
            logger.info(f"Updated {strategy_key} effectiveness: {current_effectiveness:.3f} -> {new_effectiveness:.3f}")
            
            # Record learning
            self.adaptation_history.append({
                "timestamp": time.time(),
                "decision": asdict(decision),
                "actual_outcome": actual_outcome,
                "outcome_quality": outcome_quality,
            })

    def _record_adaptation_decision(self, situation: ContextualSituation, decision: AdaptationDecision):
        """Record an adaptation decision for future learning.
        
        Args:
            situation: The contextual situation
            decision: The adaptation decision
        """
        situation_key = f"{situation.task_type}_{situation.language}"
        
        if situation_key not in self.situation_patterns:
            self.situation_patterns[situation_key] = {
                "complexity_levels": [],
                "error_patterns": [],
                "performance_patterns": [],
                "successful_strategies": [],
            }
        
        pattern = self.situation_patterns[situation_key]
        pattern["complexity_levels"].append(situation.complexity_level)
        pattern["error_patterns"].extend(situation.error_history[-5:])  # Keep recent errors
        pattern["performance_patterns"].extend(situation.performance_history[-10:])  # Keep recent performance
        pattern["successful_strategies"].append(decision.strategy.value)

    # Assessment methods
    def _assess_complexity(self, situation: ContextualSituation) -> float:
        """Assess the complexity of the situation."""
        return situation.complexity_level

    def _assess_error_severity(self, situation: ContextualSituation) -> float:
        """Assess the severity of errors in the situation."""
        if not situation.error_history:
            return 0.0
        
        # Simple heuristic: more recent errors are more severe
        recent_errors = situation.error_history[-5:]
        severity_keywords = ["critical", "fatal", "exception", "error", "failure"]
        
        severity_score = 0.0
        for error in recent_errors:
            for keyword in severity_keywords:
                if keyword.lower() in error.lower():
                    severity_score += 0.2
        
        return min(1.0, severity_score)

    def _assess_performance_trend(self, situation: ContextualSituation) -> float:
        """Assess the performance trend."""
        if len(situation.performance_history) < 2:
            return 0.5
        
        recent_performance = situation.performance_history[-5:]
        if len(recent_performance) < 2:
            return 0.5
        
        # Calculate trend
        trend = (recent_performance[-1] - recent_performance[0]) / len(recent_performance)
        return max(0.0, min(1.0, 0.5 + trend))

    def _assess_stability(self, situation: ContextualSituation) -> float:
        """Assess the stability of the situation."""
        if len(situation.performance_history) < 3:
            return 0.5
        
        # Calculate variance in performance
        variance = np.var(situation.performance_history[-10:])
        stability = 1.0 - min(variance, 1.0)
        
        return stability

    def _assess_learning_potential(self, situation: ContextualSituation) -> float:
        """Assess the learning potential of the situation."""
        # High learning potential if:
        # - Low success rate (room for improvement)
        # - Consistent error patterns (learnable)
        # - Variable performance (optimization opportunities)
        
        improvement_potential = 1.0 - situation.success_rate
        pattern_consistency = len(set(situation.error_history[-5:])) / max(len(situation.error_history[-5:]), 1)
        performance_variance = np.var(situation.performance_history[-5:]) if len(situation.performance_history) >= 2 else 0.5
        
        learning_potential = (improvement_potential + pattern_consistency + performance_variance) / 3
        return min(1.0, learning_potential)

    def _assess_risk_level(self, situation: ContextualSituation) -> float:
        """Assess the risk level of the situation."""
        # High risk if:
        # - Low success rate
        # - High error severity
        # - Critical task type
        
        success_risk = 1.0 - situation.success_rate
        error_risk = self._assess_error_severity(situation)
        
        # Task type risk (simple heuristic)
        critical_tasks = ["deployment", "production", "database", "security"]
        task_risk = 0.8 if any(task in situation.task_type.lower() for task in critical_tasks) else 0.2
        
        overall_risk = (success_risk + error_risk + task_risk) / 3
        return min(1.0, overall_risk)

    def _calculate_overall_context_score(self, analysis: Dict[str, Any]) -> float:
        """Calculate overall context score."""
        scores = [
            analysis["complexity_score"],
            1.0 - analysis["error_severity"],  # Invert error severity
            analysis["performance_trend"],
            analysis["stability_score"],
            analysis["learning_potential"],
            1.0 - analysis["risk_level"],  # Invert risk level
        ]
        
        return sum(scores) / len(scores)

    def _calculate_pattern_match(self, situation: ContextualSituation, pattern: Dict[str, Any]) -> float:
        """Calculate how well the situation matches a known pattern."""
        # Simple pattern matching based on complexity and performance
        if not pattern["complexity_levels"] or not pattern["performance_patterns"]:
            return 0.0
        
        complexity_match = 1.0 - abs(situation.complexity_level - np.mean(pattern["complexity_levels"]))
        
        if situation.performance_history:
            recent_performance = np.mean(situation.performance_history[-3:])
            pattern_performance = np.mean(pattern["performance_patterns"][-10:])
            performance_match = 1.0 - abs(recent_performance - pattern_performance)
        else:
            performance_match = 0.5
        
        return (complexity_match + performance_match) / 2
