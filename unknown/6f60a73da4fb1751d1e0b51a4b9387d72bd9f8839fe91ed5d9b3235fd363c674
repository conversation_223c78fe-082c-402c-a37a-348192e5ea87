"""
Tmux tool for the Advanced AI Agent.
"""

import os
import subprocess
import time
import platform
from typing import Dict, List, Optional, Any, Union, Tuple

class TmuxTool:
    """Tmux tool for terminal multiplexing."""

    def __init__(self):
        """Initialize the Tmux tool."""
        self.history: List[Dict[str, str]] = []
        self.sessions: Dict[str, Dict[str, Any]] = {}
        self.session_counter = 0

        # Check if tmux is installed
        self.is_tmux_available = self._check_tmux_available()

        # Check if we're on Windows
        self.is_windows = platform.system() == "Windows"

    def _check_tmux_available(self) -> bool:
        """Check if tmux is available.

        Returns:
            Whether tmux is available.
        """
        try:
            subprocess.run(["tmux", "-V"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
            return True
        except (subprocess.SubprocessError, FileNotFoundError):
            return False

    def new_session(self, command: str) -> Tuple[str, str]:
        """Create a new tmux session.

        Args:
            command: The command to run in the session.

        Returns:
            A tuple of (session_id, output).
        """
        # Add to history
        self.history.append({"action": "new_session", "command": command})

        # Check if tmux is available
        if not self.is_tmux_available:
            error_message = "Tmux is not available. Please install tmux."
            self.history[-1]["error"] = error_message
            return "", error_message

        try:
            # Generate a session name
            self.session_counter += 1
            session_name = f"aai_{self.session_counter}"

            # Create the session
            # If command is 'bash' and we're on Windows, use PowerShell instead
            if self.is_windows and command == "bash":
                command = "powershell"

            subprocess.run(
                ["tmux", "new-session", "-d", "-s", session_name, command],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )

            # Store the session
            self.sessions[session_name] = {
                "command": command,
                "created_at": time.time()
            }

            # Add to history
            self.history[-1]["session_id"] = session_name

            return session_name, f"Session created: {session_name}"

        except subprocess.SubprocessError as e:
            error_message = f"Error creating session: {e}"
            self.history[-1]["error"] = error_message
            return "", error_message

    def send_keys(self, session_id: str, keys: str) -> Tuple[bool, str]:
        """Send keys to a tmux session.

        Args:
            session_id: The ID of the session to send keys to.
            keys: The keys to send.

        Returns:
            A tuple of (success, message).
        """
        # Add to history
        self.history.append({"action": "send_keys", "session_id": session_id, "keys": keys})

        # Check if tmux is available
        if not self.is_tmux_available:
            error_message = "Tmux is not available. Please install tmux."
            self.history[-1]["error"] = error_message
            return False, error_message

        # Check if the session exists
        if session_id not in self.sessions:
            error_message = f"Session not found: {session_id}"
            self.history[-1]["error"] = error_message
            return False, error_message

        try:
            # Send the keys
            subprocess.run(
                ["tmux", "send-keys", "-t", session_id, keys, "Enter"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )

            return True, f"Keys sent to session: {session_id}"

        except subprocess.SubprocessError as e:
            error_message = f"Error sending keys: {e}"
            self.history[-1]["error"] = error_message
            return False, error_message

    def inspect_pane(self, session_id: str) -> Tuple[bool, str]:
        """Inspect a tmux pane.

        Args:
            session_id: The ID of the session to inspect.

        Returns:
            A tuple of (success, content).
        """
        # Add to history
        self.history.append({"action": "inspect_pane", "session_id": session_id})

        # Check if tmux is available
        if not self.is_tmux_available:
            error_message = "Tmux is not available. Please install tmux."
            self.history[-1]["error"] = error_message
            return False, error_message

        # Check if the session exists
        if session_id not in self.sessions:
            error_message = f"Session not found: {session_id}"
            self.history[-1]["error"] = error_message
            return False, error_message

        try:
            # Capture the pane content
            result = subprocess.run(
                ["tmux", "capture-pane", "-p", "-t", session_id],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True,
                text=True
            )

            return True, result.stdout

        except subprocess.SubprocessError as e:
            error_message = f"Error inspecting pane: {e}"
            self.history[-1]["error"] = error_message
            return False, error_message

    def kill_session(self, session_id: str) -> Tuple[bool, str]:
        """Kill a tmux session.

        Args:
            session_id: The ID of the session to kill.

        Returns:
            A tuple of (success, message).
        """
        # Add to history
        self.history.append({"action": "kill_session", "session_id": session_id})

        # Check if tmux is available
        if not self.is_tmux_available:
            error_message = "Tmux is not available. Please install tmux."
            self.history[-1]["error"] = error_message
            return False, error_message

        # Check if the session exists
        if session_id not in self.sessions:
            error_message = f"Session not found: {session_id}"
            self.history[-1]["error"] = error_message
            return False, error_message

        try:
            # Kill the session
            subprocess.run(
                ["tmux", "kill-session", "-t", session_id],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )

            # Remove the session from the list
            del self.sessions[session_id]

            return True, f"Session killed: {session_id}"

        except subprocess.SubprocessError as e:
            error_message = f"Error killing session: {e}"
            self.history[-1]["error"] = error_message
            return False, error_message

    def list_sessions(self) -> List[Dict[str, Any]]:
        """List all tmux sessions.

        Returns:
            A list of session information.
        """
        # Add to history
        self.history.append({"action": "list_sessions"})

        # Check if tmux is available
        if not self.is_tmux_available:
            self.history[-1]["error"] = "Tmux is not available. Please install tmux."
            return []

        try:
            # List the sessions
            result = subprocess.run(
                ["tmux", "list-sessions"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True,
                text=True
            )

            # Parse the output
            sessions = []
            for line in result.stdout.splitlines():
                if ":" in line:
                    session_name = line.split(":")[0]
                    sessions.append({
                        "id": session_name,
                        "command": self.sessions.get(session_name, {}).get("command", "Unknown"),
                        "created_at": self.sessions.get(session_name, {}).get("created_at", 0)
                    })

            return sessions

        except subprocess.SubprocessError:
            # No sessions or error
            return []

    def get_history(self) -> List[Dict[str, str]]:
        """Get the tmux history.

        Returns:
            The tmux history.
        """
        return self.history
