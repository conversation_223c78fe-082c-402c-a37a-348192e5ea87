"""
Enhanced AI Agent with Self-Analyzing Intelligence, Advanced Code Understanding, and RAG capabilities.
This is the main integration point for all advanced features.
"""

import time
import logging
import threading
import asyncio
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum
import json
import uuid

from .self_analyzing_intelligence import (
    SelfAnalyzingIntelligence, AnalysisType, AnalysisResult, AdaptiveDecision
)
from .advanced_code_understanding import (
    AdvancedCodeUnderstanding, LanguageType, CodeElementType, SearchResult
)
from .rag_enhanced_system import (
    RAGEnhancedSystem, RAGMode, ContextType, GenerationRequest, GenerationResult
)
from .enhanced_performance_monitor import EnhancedPerformanceMonitor
from .advanced_cache_system import AdvancedCacheSystem
from ..tools.optimized_search_engine import OptimizedSearchEngine
from ..tools.threading.thread_manager import ThreadManager

logger = logging.getLogger(__name__)

class AgentMode(Enum):
    """Enhanced agent operation modes."""
    INTERACTIVE = "interactive"
    AUTONOMOUS = "autonomous"
    COLLABORATIVE = "collaborative"
    LEARNING = "learning"
    DEBUGGING = "debugging"

class TaskType(Enum):
    """Types of tasks the enhanced agent can handle."""
    CODE_GENERATION = "code_generation"
    CODE_ANALYSIS = "code_analysis"
    DEBUGGING = "debugging"
    REFACTORING = "refactoring"
    TESTING = "testing"
    DOCUMENTATION = "documentation"
    OPTIMIZATION = "optimization"
    LEARNING = "learning"
    RESEARCH = "research"

@dataclass
class EnhancedRequest:
    """Enhanced request with comprehensive context."""
    request_id: str
    task_type: TaskType
    user_input: str
    context: Dict[str, Any]
    preferences: Dict[str, Any]
    constraints: List[str]
    success_criteria: List[str]
    mode: AgentMode
    priority: int
    timeout_seconds: float

@dataclass
class EnhancedResponse:
    """Enhanced response with detailed analysis and reasoning."""
    request_id: str
    success: bool
    result: Any
    confidence_score: float
    reasoning_steps: List[str]
    analysis_results: List[AnalysisResult]
    context_used: List[Any]
    recommendations: List[str]
    alternatives: List[str]
    execution_time: float
    metadata: Dict[str, Any]

class EnhancedAgent:
    """Enhanced AI Agent with comprehensive capabilities."""
    
    def __init__(self, model_manager, workspace_dir: Path, config: Dict[str, Any] = None):
        """Initialize the enhanced agent.
        
        Args:
            model_manager: The model manager for AI operations
            workspace_dir: The workspace directory
            config: Optional configuration dictionary
        """
        self.model_manager = model_manager
        self.workspace_dir = workspace_dir
        self.config = config or {}
        self.lock = threading.RLock()
        
        # Initialize core systems
        self.self_analyzer = SelfAnalyzingIntelligence(model_manager, workspace_dir)
        self.code_understanding = AdvancedCodeUnderstanding(workspace_dir, model_manager)
        self.rag_system = RAGEnhancedSystem(model_manager, workspace_dir, self.code_understanding)
        
        # Initialize supporting systems
        self.performance_monitor = EnhancedPerformanceMonitor(workspace_dir)
        self.cache_system = AdvancedCacheSystem(
            persistence_path=workspace_dir / "agent_cache.db"
        )
        self.search_engine = OptimizedSearchEngine(
            cache_dir=workspace_dir / "search_cache"
        )
        self.thread_manager = ThreadManager(max_workers=4, enable_monitoring=True)
        
        # Agent state
        self.current_mode = AgentMode.INTERACTIVE
        self.active_tasks: Dict[str, EnhancedRequest] = {}
        self.task_history: List[EnhancedResponse] = []
        
        # Performance metrics
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'average_response_time': 0.0,
            'average_confidence': 0.0,
            'cache_hit_rate': 0.0
        }
        
        # Callbacks for external integration
        self.request_callbacks: List[Callable[[EnhancedRequest], None]] = []
        self.response_callbacks: List[Callable[[EnhancedResponse], None]] = []
        
        # Initialize systems
        self._initialize_systems()
    
    def _initialize_systems(self):
        """Initialize all subsystems."""
        logger.info("Initializing enhanced agent systems...")
        
        # Start performance monitoring
        self.performance_monitor.start_monitoring()
        
        # Start cache cleanup
        self.cache_system.start_cleanup()
        
        # Index codebase
        self.code_understanding.index_codebase()
        
        # Update RAG context from codebase
        self.rag_system.update_context_from_codebase()
        
        # Set up callbacks for integration
        self.self_analyzer.add_analysis_callback(self._on_analysis_result)
        self.self_analyzer.add_decision_callback(self._on_adaptive_decision)
        
        logger.info("Enhanced agent systems initialized successfully")
    
    def process_enhanced_request(self, request: EnhancedRequest) -> EnhancedResponse:
        """Process an enhanced request with full capabilities.
        
        Args:
            request: Enhanced request with comprehensive context
            
        Returns:
            Enhanced response with detailed analysis
        """
        with self.lock:
            start_time = time.time()
            logger.info(f"Processing enhanced request: {request.task_type.value}")
            
            # Add to active tasks
            self.active_tasks[request.request_id] = request
            
            # Trigger request callbacks
            for callback in self.request_callbacks:
                try:
                    callback(request)
                except Exception as e:
                    logger.error(f"Error in request callback: {e}")
            
            try:
                # Check cache first
                cached_response = self._check_cache(request)
                if cached_response:
                    logger.info("Using cached response")
                    return cached_response
                
                # Route to appropriate handler based on task type
                response = self._route_request(request)
                
                # Post-process response
                response = self._post_process_response(response, request)
                
                # Cache successful responses
                if response.success and response.confidence_score > 0.7:
                    self._cache_response(request, response)
                
                # Update metrics
                self._update_metrics(response)
                
                # Add to history
                self.task_history.append(response)
                
                # Trigger response callbacks
                for callback in self.response_callbacks:
                    try:
                        callback(response)
                    except Exception as e:
                        logger.error(f"Error in response callback: {e}")
                
                logger.info(f"Request processed in {response.execution_time:.2f}s "
                           f"with confidence {response.confidence_score:.2f}")
                
                return response
                
            except Exception as e:
                logger.error(f"Error processing request: {e}", exc_info=True)
                return self._create_error_response(request, str(e), time.time() - start_time)
            
            finally:
                # Remove from active tasks
                self.active_tasks.pop(request.request_id, None)
    
    def _route_request(self, request: EnhancedRequest) -> EnhancedResponse:
        """Route request to appropriate handler based on task type."""
        handlers = {
            TaskType.CODE_GENERATION: self._handle_code_generation,
            TaskType.CODE_ANALYSIS: self._handle_code_analysis,
            TaskType.DEBUGGING: self._handle_debugging,
            TaskType.REFACTORING: self._handle_refactoring,
            TaskType.TESTING: self._handle_testing,
            TaskType.DOCUMENTATION: self._handle_documentation,
            TaskType.OPTIMIZATION: self._handle_optimization,
            TaskType.LEARNING: self._handle_learning,
            TaskType.RESEARCH: self._handle_research
        }
        
        handler = handlers.get(request.task_type, self._handle_generic)
        return handler(request)
    
    def _handle_code_generation(self, request: EnhancedRequest) -> EnhancedResponse:
        """Handle code generation requests with RAG and self-analysis."""
        start_time = time.time()
        
        # Create RAG generation request
        rag_request = GenerationRequest(
            request_id=request.request_id,
            mode=RAGMode.CODE_GENERATION,
            prompt=request.user_input,
            context_requirements=[ContextType.CODE_CONTEXT, ContextType.PROJECT_CONTEXT],
            constraints=request.constraints,
            preferences=request.preferences,
            max_context_items=10,
            temperature=0.7,
            max_tokens=2048
        )
        
        # Generate with RAG
        rag_result = self.rag_system.generate_with_rag(rag_request)
        
        # Self-analyze the generated code
        analysis_results = self.self_analyzer.analyze_and_improve(
            initial_input=rag_result.generated_content,
            analysis_type=AnalysisType.CODE_EXECUTION,
            success_criteria=request.success_criteria,
            constraints=request.constraints
        )
        
        # Determine final result
        if analysis_results and analysis_results[-1].success:
            final_code = rag_result.generated_content
            confidence = (rag_result.confidence_score + analysis_results[-1].confidence_score) / 2
        else:
            final_code = rag_result.generated_content
            confidence = rag_result.confidence_score * 0.8  # Reduce confidence if analysis failed
        
        return EnhancedResponse(
            request_id=request.request_id,
            success=True,
            result={
                'generated_code': final_code,
                'rag_result': asdict(rag_result),
                'analysis_iterations': len(analysis_results)
            },
            confidence_score=confidence,
            reasoning_steps=rag_result.reasoning_steps + [a.findings for a in analysis_results],
            analysis_results=analysis_results,
            context_used=rag_result.context_used,
            recommendations=rag_result.alternatives,
            alternatives=[],
            execution_time=time.time() - start_time,
            metadata={
                'task_type': request.task_type.value,
                'rag_confidence': rag_result.confidence_score,
                'analysis_confidence': analysis_results[-1].confidence_score if analysis_results else 0.0
            }
        )
    
    def _handle_code_analysis(self, request: EnhancedRequest) -> EnhancedResponse:
        """Handle code analysis requests."""
        start_time = time.time()
        
        # Extract code from request
        code = request.context.get('code', request.user_input)
        file_path = request.context.get('file_path', '')
        
        # Perform semantic search for similar code
        search_results = self.code_understanding.semantic_search(
            query=f"analyze {code[:100]}...",
            max_results=5
        )
        
        # Self-analyze the code
        analysis_results = self.self_analyzer.analyze_and_improve(
            initial_input=code,
            analysis_type=AnalysisType.CORRECTNESS,
            success_criteria=request.success_criteria or ['code_quality', 'best_practices'],
            constraints=request.constraints
        )
        
        # Get code insights if file path provided
        insights = {}
        if file_path:
            insights = self.code_understanding.get_code_insights(file_path)
        
        return EnhancedResponse(
            request_id=request.request_id,
            success=True,
            result={
                'analysis_summary': analysis_results[-1].findings if analysis_results else [],
                'code_insights': insights,
                'similar_code': [asdict(sr) for sr in search_results],
                'recommendations': analysis_results[-1].recommendations if analysis_results else []
            },
            confidence_score=analysis_results[-1].confidence_score if analysis_results else 0.5,
            reasoning_steps=[a.findings for a in analysis_results],
            analysis_results=analysis_results,
            context_used=search_results,
            recommendations=analysis_results[-1].recommendations if analysis_results else [],
            alternatives=[],
            execution_time=time.time() - start_time,
            metadata={'task_type': request.task_type.value}
        )
    
    def _handle_debugging(self, request: EnhancedRequest) -> EnhancedResponse:
        """Handle debugging requests with intelligent analysis."""
        start_time = time.time()
        
        # Create RAG request for debugging
        rag_request = GenerationRequest(
            request_id=request.request_id,
            mode=RAGMode.DEBUGGING,
            prompt=request.user_input,
            context_requirements=[ContextType.CODE_CONTEXT, ContextType.HISTORICAL_CONTEXT],
            constraints=request.constraints,
            preferences=request.preferences,
            max_context_items=8,
            temperature=0.3,  # Lower temperature for debugging
            max_tokens=1024
        )
        
        # Generate debugging solution
        rag_result = self.rag_system.generate_with_rag(rag_request)
        
        # Self-analyze the debugging approach
        analysis_results = self.self_analyzer.analyze_and_improve(
            initial_input=rag_result.generated_content,
            analysis_type=AnalysisType.DEBUGGING,
            success_criteria=request.success_criteria or ['error_resolution', 'root_cause_identified'],
            constraints=request.constraints
        )
        
        return EnhancedResponse(
            request_id=request.request_id,
            success=True,
            result={
                'debugging_solution': rag_result.generated_content,
                'analysis_steps': [a.findings for a in analysis_results],
                'confidence_assessment': analysis_results[-1].confidence_score if analysis_results else 0.5
            },
            confidence_score=rag_result.confidence_score,
            reasoning_steps=rag_result.reasoning_steps,
            analysis_results=analysis_results,
            context_used=rag_result.context_used,
            recommendations=rag_result.alternatives,
            alternatives=[],
            execution_time=time.time() - start_time,
            metadata={'task_type': request.task_type.value}
        )
