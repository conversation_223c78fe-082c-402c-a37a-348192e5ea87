"""
Autonomous Execution Engine - Core engine for autonomous task execution.

This engine provides:
1. Continuous task execution without user intervention
2. Intelligent decision making and next step planning
3. Error handling and recovery mechanisms
4. Progress monitoring and reporting
5. Adaptive workflow management
"""

import asyncio
import threading
import time
import logging
import json
import uuid
from typing import Dict, List, Optional, Any, Callable, Union, Tuple
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

from .autonomous_agent_framework import (
    AutonomousTask, AutonomousWorkflow, WorkflowStep, TaskStatus, TaskPriority,
    AgentMode, ResearchResult, TaskPersistenceManager, AutonomousResearchEngine
)
from .iterative_improvement_system import IterativeImprovementSystem

logger = logging.getLogger(__name__)

class ExecutionDecision(Enum):
    """Decisions the execution engine can make."""
    CONTINUE = "continue"
    PAUSE = "pause"
    RETRY = "retry"
    ESCALATE = "escalate"
    ABORT = "abort"
    RESEARCH_MORE = "research_more"
    BREAK_DOWN_TASK = "break_down_task"

@dataclass
class ExecutionContext:
    """Context for autonomous execution."""
    current_task: Optional[AutonomousTask] = None
    current_workflow: Optional[AutonomousWorkflow] = None
    execution_history: List[Dict[str, Any]] = field(default_factory=list)
    available_tools: Dict[str, Any] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    session_data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ExecutionResult:
    """Result of autonomous execution step."""
    success: bool
    result: Any
    decision: ExecutionDecision
    next_steps: List[str]
    confidence: float
    execution_time: float
    error: Optional[str] = None
    recommendations: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

class AutonomousExecutionEngine:
    """Core engine for autonomous task execution."""
    
    def __init__(self, 
                 model_manager,
                 persistence_manager: TaskPersistenceManager,
                 research_engine: AutonomousResearchEngine,
                 tools: Dict[str, Any],
                 workspace_dir: Path):
        self.model_manager = model_manager
        self.persistence_manager = persistence_manager
        self.research_engine = research_engine
        self.tools = tools
        self.workspace_dir = workspace_dir
        
        # Execution state
        self.is_running = False
        self.current_mode = AgentMode.INTERACTIVE
        self.execution_context = ExecutionContext()
        self.execution_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # Configuration
        self.max_execution_time = timedelta(hours=2)  # Maximum time for single task
        self.checkpoint_interval = timedelta(minutes=10)  # How often to save checkpoints
        self.retry_limit = 3
        self.confidence_threshold = 0.7
        
        # Callbacks for user interaction
        self.progress_callback: Optional[Callable] = None
        self.decision_callback: Optional[Callable] = None
        self.completion_callback: Optional[Callable] = None
        
        logger.info("Autonomous execution engine initialized")
    
    def start_autonomous_mode(self, 
                            initial_task: str,
                            mode: AgentMode = AgentMode.AUTONOMOUS,
                            constraints: Dict[str, Any] = None):
        """Start autonomous operation mode."""
        if self.is_running:
            logger.warning("Autonomous mode already running")
            return
        
        logger.info(f"Starting autonomous mode: {mode.value}")
        self.current_mode = mode
        self.is_running = True
        self.stop_event.clear()
        
        # Create initial task
        task = self._create_initial_task(initial_task, constraints or {})
        self.persistence_manager.save_task(task)
        
        # Start execution thread
        self.execution_thread = threading.Thread(
            target=self._autonomous_execution_loop,
            args=(task,),
            daemon=True
        )
        self.execution_thread.start()
        
        logger.info("Autonomous execution started")
    
    def stop_autonomous_mode(self):
        """Stop autonomous operation mode."""
        if not self.is_running:
            return
        
        logger.info("Stopping autonomous mode")
        self.is_running = False
        self.stop_event.set()
        
        if self.execution_thread and self.execution_thread.is_alive():
            self.execution_thread.join(timeout=10.0)
        
        self.current_mode = AgentMode.INTERACTIVE
        logger.info("Autonomous mode stopped")
    
    def _create_initial_task(self, description: str, constraints: Dict[str, Any]) -> AutonomousTask:
        """Create the initial autonomous task."""
        task_id = str(uuid.uuid4())
        
        # Analyze the task to determine success criteria and next steps
        analysis = self._analyze_task_requirements(description)
        
        task = AutonomousTask(
            task_id=task_id,
            title=f"Autonomous Task: {description[:50]}...",
            description=description,
            status=TaskStatus.PENDING,
            priority=TaskPriority.HIGH,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            context={"constraints": constraints, "analysis": analysis},
            success_criteria=analysis.get("success_criteria", []),
            next_steps=analysis.get("initial_steps", [])
        )
        
        return task
    
    def _analyze_task_requirements(self, description: str) -> Dict[str, Any]:
        """Analyze task requirements to determine approach."""
        # Use the model to analyze the task
        analysis_prompt = f"""
        Analyze this task and provide a structured approach:
        
        Task: {description}
        
        Please provide:
        1. Success criteria (what defines completion)
        2. Initial steps to take
        3. Potential challenges
        4. Required research areas
        5. Estimated complexity (1-10)
        6. Required tools/capabilities
        
        Format as JSON.
        """
        
        try:
            response = self.model_manager.generate(prompt=analysis_prompt)
            # Parse JSON response (simplified for now)
            return {
                "success_criteria": [
                    "Task requirements clearly understood",
                    "Implementation completed successfully",
                    "Solution tested and validated",
                    "Documentation provided"
                ],
                "initial_steps": [
                    "Research task requirements",
                    "Plan implementation approach",
                    "Begin implementation",
                    "Test and validate solution"
                ],
                "challenges": ["Complex requirements", "Technical constraints"],
                "research_areas": [description],
                "complexity": 7,
                "required_tools": ["web", "code", "file"]
            }
        except Exception as e:
            logger.error(f"Error analyzing task requirements: {e}")
            return {
                "success_criteria": ["Complete the requested task"],
                "initial_steps": ["Begin task execution"],
                "challenges": [],
                "research_areas": [description],
                "complexity": 5,
                "required_tools": []
            }
    
    def _autonomous_execution_loop(self, initial_task: AutonomousTask):
        """Main autonomous execution loop."""
        logger.info(f"Starting autonomous execution for task: {initial_task.task_id}")
        
        current_task = initial_task
        last_checkpoint = datetime.now()
        
        try:
            while self.is_running and not self.stop_event.is_set():
                # Update execution context
                self.execution_context.current_task = current_task
                
                # Execute next step
                result = self._execute_task_step(current_task)
                
                # Process execution result
                decision = self._process_execution_result(current_task, result)
                
                # Take action based on decision
                next_task = self._handle_execution_decision(current_task, result, decision)
                
                # Update task status and progress
                self._update_task_progress(current_task, result)
                
                # Save checkpoint if needed
                if datetime.now() - last_checkpoint > self.checkpoint_interval:
                    self._save_checkpoint(current_task)
                    last_checkpoint = datetime.now()
                
                # Check if task is complete
                if current_task.status == TaskStatus.COMPLETED:
                    logger.info(f"Task completed: {current_task.task_id}")
                    if self.completion_callback:
                        self.completion_callback(current_task)
                    break
                
                # Check if we should continue with next task
                if next_task and next_task.task_id != current_task.task_id:
                    current_task = next_task
                
                # Brief pause to prevent overwhelming
                time.sleep(1)
                
        except Exception as e:
            logger.error(f"Error in autonomous execution loop: {e}")
            current_task.status = TaskStatus.FAILED
            current_task.results["error"] = str(e)
            self.persistence_manager.save_task(current_task)
        
        finally:
            logger.info("Autonomous execution loop ended")
    
    def _execute_task_step(self, task: AutonomousTask) -> ExecutionResult:
        """Execute a single step of the task."""
        start_time = time.time()
        
        try:
            # Determine next action based on task state
            if not task.next_steps:
                # Need to plan next steps
                return self._plan_next_steps(task)
            
            # Get the next step to execute
            next_step = task.next_steps[0]
            
            # Execute the step based on its type
            if "research" in next_step.lower():
                return self._execute_research_step(task, next_step)
            elif "implement" in next_step.lower() or "code" in next_step.lower():
                return self._execute_implementation_step(task, next_step)
            elif "test" in next_step.lower():
                return self._execute_testing_step(task, next_step)
            elif "validate" in next_step.lower():
                return self._execute_validation_step(task, next_step)
            else:
                return self._execute_general_step(task, next_step)
                
        except Exception as e:
            logger.error(f"Error executing task step: {e}")
            return ExecutionResult(
                success=False,
                result=None,
                decision=ExecutionDecision.RETRY,
                next_steps=[],
                confidence=0.0,
                execution_time=time.time() - start_time,
                error=str(e)
            )

    def _execute_research_step(self, task: AutonomousTask, step: str) -> ExecutionResult:
        """Execute a research step."""
        start_time = time.time()

        try:
            # Extract research topic from step
            research_topic = self._extract_research_topic(step, task)

            # Conduct autonomous research
            research_result = asyncio.run(
                self.research_engine.conduct_research(research_topic)
            )

            # Store research results in task
            if "research_data" not in task.results:
                task.results["research_data"] = []
            task.results["research_data"].append({
                "topic": research_topic,
                "result": research_result,
                "timestamp": datetime.now().isoformat()
            })

            # Determine next steps based on research
            next_steps = self._determine_post_research_steps(task, research_result)

            return ExecutionResult(
                success=True,
                result=research_result,
                decision=ExecutionDecision.CONTINUE,
                next_steps=next_steps,
                confidence=research_result.confidence_score,
                execution_time=time.time() - start_time,
                recommendations=[f"Research completed on {research_topic}"]
            )

        except Exception as e:
            logger.error(f"Error in research step: {e}")
            return ExecutionResult(
                success=False,
                result=None,
                decision=ExecutionDecision.RETRY,
                next_steps=[],
                confidence=0.0,
                execution_time=time.time() - start_time,
                error=str(e)
            )

    def _execute_implementation_step(self, task: AutonomousTask, step: str) -> ExecutionResult:
        """Execute an implementation step."""
        start_time = time.time()

        try:
            # Generate implementation based on research and requirements
            implementation_prompt = f"""
            Based on the task and research, implement the next step:

            Task: {task.description}
            Current Step: {step}
            Research Data: {json.dumps(task.results.get("research_data", []), indent=2)}

            Provide specific implementation code or actions.
            """

            response = self.model_manager.generate(prompt=implementation_prompt)

            # Execute the implementation using appropriate tools
            implementation_result = self._execute_with_tools(response, task)

            # Store implementation results
            if "implementations" not in task.results:
                task.results["implementations"] = []
            task.results["implementations"].append({
                "step": step,
                "implementation": response,
                "result": implementation_result,
                "timestamp": datetime.now().isoformat()
            })

            next_steps = ["Test the implementation", "Validate results"]

            return ExecutionResult(
                success=True,
                result=implementation_result,
                decision=ExecutionDecision.CONTINUE,
                next_steps=next_steps,
                confidence=0.8,
                execution_time=time.time() - start_time,
                recommendations=["Implementation completed successfully"]
            )

        except Exception as e:
            logger.error(f"Error in implementation step: {e}")
            return ExecutionResult(
                success=False,
                result=None,
                decision=ExecutionDecision.RETRY,
                next_steps=[],
                confidence=0.0,
                execution_time=time.time() - start_time,
                error=str(e)
            )

    def _execute_testing_step(self, task: AutonomousTask, step: str) -> ExecutionResult:
        """Execute a testing step."""
        start_time = time.time()

        try:
            # Generate tests based on implementations
            implementations = task.results.get("implementations", [])
            if not implementations:
                return ExecutionResult(
                    success=False,
                    result=None,
                    decision=ExecutionDecision.PAUSE,
                    next_steps=["Complete implementation first"],
                    confidence=0.0,
                    execution_time=time.time() - start_time,
                    error="No implementations to test"
                )

            # Create and run tests
            test_results = []
            for impl in implementations:
                test_result = self._run_tests_for_implementation(impl)
                test_results.append(test_result)

            # Store test results
            task.results["test_results"] = test_results

            # Determine if tests passed
            all_passed = all(result.get("passed", False) for result in test_results)

            if all_passed:
                next_steps = ["Validate final solution"]
                decision = ExecutionDecision.CONTINUE
            else:
                next_steps = ["Fix failing tests", "Retry implementation"]
                decision = ExecutionDecision.RETRY

            return ExecutionResult(
                success=all_passed,
                result=test_results,
                decision=decision,
                next_steps=next_steps,
                confidence=0.9 if all_passed else 0.4,
                execution_time=time.time() - start_time,
                recommendations=["Testing completed"] if all_passed else ["Some tests failed"]
            )

        except Exception as e:
            logger.error(f"Error in testing step: {e}")
            return ExecutionResult(
                success=False,
                result=None,
                decision=ExecutionDecision.RETRY,
                next_steps=[],
                confidence=0.0,
                execution_time=time.time() - start_time,
                error=str(e)
            )

    # Helper methods for execution steps
    def _extract_research_topic(self, step: str, task: AutonomousTask) -> str:
        """Extract research topic from step description."""
        # Simple extraction - can be enhanced with NLP
        if "research" in step.lower():
            # Try to extract topic after "research"
            parts = step.lower().split("research")
            if len(parts) > 1:
                topic = parts[1].strip()
                if topic:
                    return topic

        # Fallback to task description
        return task.description

    def _determine_post_research_steps(self, task: AutonomousTask, research_result) -> List[str]:
        """Determine next steps after research completion."""
        if research_result.confidence_score > 0.8:
            return [
                "Plan implementation based on research",
                "Begin implementation",
                "Test implementation"
            ]
        else:
            return [
                "Conduct additional research",
                "Refine research approach",
                "Seek alternative sources"
            ]

    def _execute_with_tools(self, response: str, task: AutonomousTask) -> Dict[str, Any]:
        """Execute response using available tools."""
        try:
            # Parse tool commands from response
            tool_commands = self._parse_tool_commands(response)
            results = {}

            for tool_name, command in tool_commands:
                if tool_name in self.tools:
                    tool = self.tools[tool_name]
                    try:
                        result = tool.execute(command)
                        results[tool_name] = result
                    except Exception as e:
                        results[tool_name] = {"error": str(e)}
                else:
                    results[tool_name] = {"error": f"Tool {tool_name} not available"}

            return results

        except Exception as e:
            logger.error(f"Error executing with tools: {e}")
            return {"error": str(e)}

    def _parse_tool_commands(self, response: str) -> List[Tuple[str, str]]:
        """Parse tool commands from response."""
        import re

        # Look for tool command patterns like ```tool_name\ncommand```
        pattern = r'```(\w+)\n(.*?)```'
        matches = re.findall(pattern, response, re.DOTALL)

        return [(tool_name, command.strip()) for tool_name, command in matches]

    def _run_tests_for_implementation(self, implementation: Dict[str, Any]) -> Dict[str, Any]:
        """Run tests for an implementation."""
        try:
            # Generate and run tests based on implementation
            test_prompt = f"""
            Generate tests for this implementation:

            Implementation: {implementation.get('implementation', '')}

            Create comprehensive tests to validate the implementation.
            """

            test_code = self.model_manager.generate(prompt=test_prompt)

            # Execute tests using code tool if available
            if "code" in self.tools:
                test_result = self.tools["code"].execute(test_code)
                return {
                    "test_code": test_code,
                    "result": test_result,
                    "passed": "error" not in str(test_result).lower()
                }
            else:
                return {
                    "test_code": test_code,
                    "result": "No code execution tool available",
                    "passed": False
                }

        except Exception as e:
            logger.error(f"Error running tests: {e}")
            return {
                "test_code": "",
                "result": str(e),
                "passed": False
            }

    def _check_success_criterion(self, task: AutonomousTask, criterion: str) -> bool:
        """Check if a success criterion is met."""
        try:
            # Use model to evaluate criterion against task results
            evaluation_prompt = f"""
            Evaluate if this success criterion is met:

            Criterion: {criterion}
            Task Results: {json.dumps(task.results, indent=2)}
            Task Progress: {task.progress}

            Answer with YES or NO and brief explanation.
            """

            response = self.model_manager.generate(prompt=evaluation_prompt)

            # Simple evaluation - can be enhanced
            return "yes" in response.lower() and "no" not in response.lower()

        except Exception as e:
            logger.error(f"Error checking success criterion: {e}")
            return False

    def _get_criterion_evidence(self, task: AutonomousTask, criterion: str) -> str:
        """Get evidence for criterion evaluation."""
        # Extract relevant evidence from task results
        evidence_parts = []

        if "research_data" in task.results:
            evidence_parts.append(f"Research completed: {len(task.results['research_data'])} topics")

        if "implementations" in task.results:
            evidence_parts.append(f"Implementations: {len(task.results['implementations'])} completed")

        if "test_results" in task.results:
            passed_tests = sum(1 for r in task.results['test_results'] if r.get('passed', False))
            evidence_parts.append(f"Tests: {passed_tests} passed")

        return "; ".join(evidence_parts) if evidence_parts else "No evidence available"

    def _process_execution_result(self, task: AutonomousTask, result: ExecutionResult) -> ExecutionDecision:
        """Process execution result and determine next action."""
        if result.success and result.confidence > self.confidence_threshold:
            return ExecutionDecision.CONTINUE
        elif result.success and result.confidence > 0.5:
            return ExecutionDecision.CONTINUE
        elif result.error and "retry" in result.error.lower():
            return ExecutionDecision.RETRY
        elif not result.success and result.confidence < 0.3:
            return ExecutionDecision.RESEARCH_MORE
        else:
            return result.decision

    def _handle_execution_decision(self,
                                 task: AutonomousTask,
                                 result: ExecutionResult,
                                 decision: ExecutionDecision) -> AutonomousTask:
        """Handle execution decision and return next task."""
        if decision == ExecutionDecision.CONTINUE:
            # Update next steps and continue
            if result.next_steps:
                task.next_steps = result.next_steps
            elif task.next_steps:
                task.next_steps = task.next_steps[1:]  # Remove completed step

            return task

        elif decision == ExecutionDecision.RETRY:
            # Increment retry count and try again
            retry_count = task.context.get("retry_count", 0) + 1
            task.context["retry_count"] = retry_count

            if retry_count > self.retry_limit:
                task.status = TaskStatus.FAILED
                task.results["failure_reason"] = "Maximum retries exceeded"

            return task

        elif decision == ExecutionDecision.RESEARCH_MORE:
            # Add research step to beginning of next steps
            research_step = f"Research more about: {task.description}"
            task.next_steps.insert(0, research_step)
            return task

        elif decision == ExecutionDecision.BREAK_DOWN_TASK:
            # Break down task into subtasks
            subtasks = self._break_down_task(task)
            for subtask in subtasks:
                self.persistence_manager.save_task(subtask)

            # Update parent task
            task.subtasks = [st.task_id for st in subtasks]
            task.status = TaskStatus.IN_PROGRESS

            return subtasks[0] if subtasks else task

        elif decision == ExecutionDecision.PAUSE:
            task.status = TaskStatus.PAUSED
            if self.current_mode == AgentMode.SEMI_AUTONOMOUS and self.decision_callback:
                self.decision_callback(task, result, decision)
            return task

        elif decision == ExecutionDecision.ABORT:
            task.status = TaskStatus.CANCELLED
            task.results["cancellation_reason"] = result.error or "Task aborted"
            return task

        else:
            return task

    def _update_task_progress(self, task: AutonomousTask, result: ExecutionResult):
        """Update task progress based on execution result."""
        if result.success:
            # Increment progress based on completed steps
            total_steps = len(task.success_criteria) * 2  # Rough estimate
            completed_steps = len(task.results.get("implementations", [])) + \
                            len(task.results.get("test_results", [])) + \
                            len(task.results.get("research_data", []))

            task.progress = min(completed_steps / max(total_steps, 1), 1.0)

        # Update timestamp
        task.updated_at = datetime.now()

        # Save updated task
        self.persistence_manager.save_task(task)

        # Notify progress callback
        if self.progress_callback:
            self.progress_callback(task, result)

    def _save_checkpoint(self, task: AutonomousTask):
        """Save a checkpoint of current task state."""
        checkpoint = {
            "timestamp": datetime.now().isoformat(),
            "task_id": task.task_id,
            "status": task.status.value,
            "progress": task.progress,
            "next_steps": task.next_steps,
            "results_summary": self._summarize_results(task.results)
        }

        task.checkpoints.append(checkpoint)
        self.persistence_manager.save_task(task)

        logger.info(f"Checkpoint saved for task {task.task_id}")

    def _break_down_task(self, task: AutonomousTask) -> List[AutonomousTask]:
        """Break down a complex task into smaller subtasks."""
        breakdown_prompt = f"""
        Break down this complex task into 3-5 smaller, manageable subtasks:

        Task: {task.description}
        Current Progress: {task.progress}
        Success Criteria: {task.success_criteria}

        Provide subtasks as a JSON list with title and description for each.
        """

        try:
            response = self.model_manager.generate(prompt=breakdown_prompt)

            # Parse subtasks (simplified)
            subtasks = []
            for i in range(3):  # Create 3 default subtasks
                subtask_id = str(uuid.uuid4())
                subtask = AutonomousTask(
                    task_id=subtask_id,
                    title=f"Subtask {i+1} of {task.title}",
                    description=f"Part {i+1} of the main task: {task.description}",
                    status=TaskStatus.PENDING,
                    priority=task.priority,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                    parent_task_id=task.task_id,
                    success_criteria=[f"Complete subtask {i+1} requirements"],
                    next_steps=[f"Begin subtask {i+1} execution"]
                )
                subtasks.append(subtask)

            return subtasks

        except Exception as e:
            logger.error(f"Error breaking down task: {e}")
            return []

    def _summarize_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Create a summary of task results."""
        summary = {}

        if "research_data" in results:
            summary["research_completed"] = len(results["research_data"])

        if "implementations" in results:
            summary["implementations_completed"] = len(results["implementations"])

        if "test_results" in results:
            passed_tests = sum(1 for r in results["test_results"] if r.get("passed", False))
            summary["tests_passed"] = passed_tests
            summary["total_tests"] = len(results["test_results"])

        if "validation" in results:
            met_criteria = sum(1 for r in results["validation"] if r.get("met", False))
            summary["criteria_met"] = met_criteria
            summary["total_criteria"] = len(results["validation"])

        return summary

    # Public interface methods
    def get_current_status(self) -> Dict[str, Any]:
        """Get current autonomous execution status."""
        return {
            "is_running": self.is_running,
            "mode": self.current_mode.value,
            "current_task": self.execution_context.current_task.task_id if self.execution_context.current_task else None,
            "active_tasks": len(self.persistence_manager.get_active_tasks()),
            "active_workflows": len(self.persistence_manager.get_active_workflows())
        }

    def set_progress_callback(self, callback: Callable):
        """Set callback for progress updates."""
        self.progress_callback = callback

    def set_decision_callback(self, callback: Callable):
        """Set callback for decision points in semi-autonomous mode."""
        self.decision_callback = callback

    def set_completion_callback(self, callback: Callable):
        """Set callback for task completion."""
        self.completion_callback = callback
    
    def _plan_next_steps(self, task: AutonomousTask) -> ExecutionResult:
        """Plan the next steps for task execution."""
        planning_prompt = f"""
        Plan the next steps for this task:
        
        Task: {task.description}
        Current Progress: {task.progress}
        Previous Results: {json.dumps(task.results, indent=2)}
        Success Criteria: {task.success_criteria}
        
        Based on the current state, what are the next 3-5 specific steps to take?
        Consider what has been done and what still needs to be accomplished.
        
        Provide steps as a JSON list of strings.
        """
        
        try:
            response = self.model_manager.generate(prompt=planning_prompt)
            # Parse the response to get next steps (simplified)
            next_steps = [
                "Continue with current approach",
                "Validate progress so far",
                "Plan next phase"
            ]
            
            return ExecutionResult(
                success=True,
                result={"planned_steps": next_steps},
                decision=ExecutionDecision.CONTINUE,
                next_steps=next_steps,
                confidence=0.8,
                execution_time=1.0
            )
            
        except Exception as e:
            logger.error(f"Error planning next steps: {e}")
            return ExecutionResult(
                success=False,
                result=None,
                decision=ExecutionDecision.PAUSE,
                next_steps=[],
                confidence=0.0,
                execution_time=1.0,
                error=str(e)
            )
