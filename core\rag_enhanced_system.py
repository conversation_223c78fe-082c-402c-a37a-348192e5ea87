"""
RAG-Enhanced Generation System for the Advanced AI Agent.
Implements Retrieval-Augmented Generation for contextual code suggestions and problem-solving.
"""

import time
import logging
import threading
import json
import hashlib
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum
import uuid
import re

logger = logging.getLogger(__name__)

class RAGMode(Enum):
    """RAG operation modes."""
    CODE_GENERATION = "code_generation"
    CODE_COMPLETION = "code_completion"
    PROBLEM_SOLVING = "problem_solving"
    DEBUGGING = "debugging"
    REFACTORING = "refactoring"
    DOCUMENTATION = "documentation"
    TESTING = "testing"

class ContextType(Enum):
    """Types of context for RAG."""
    CODE_CONTEXT = "code_context"
    PROJECT_CONTEXT = "project_context"
    DOMAIN_CONTEXT = "domain_context"
    HISTORICAL_CONTEXT = "historical_context"
    SEMANTIC_CONTEXT = "semantic_context"

@dataclass
class RAGContext:
    """Context information for RAG operations."""
    context_id: str
    context_type: ContextType
    content: str
    metadata: Dict[str, Any]
    relevance_score: float
    timestamp: float
    source: str
    tags: List[str]

@dataclass
class GenerationRequest:
    """Request for RAG-enhanced generation."""
    request_id: str
    mode: RAGMode
    prompt: str
    context_requirements: List[ContextType]
    constraints: List[str]
    preferences: Dict[str, Any]
    max_context_items: int
    temperature: float
    max_tokens: int

@dataclass
class GenerationResult:
    """Result of RAG-enhanced generation."""
    request_id: str
    generated_content: str
    confidence_score: float
    context_used: List[RAGContext]
    reasoning_steps: List[str]
    alternatives: List[str]
    execution_time: float
    metadata: Dict[str, Any]

class RAGEnhancedSystem:
    """RAG-Enhanced Generation System with contextual intelligence."""
    
    def __init__(self, model_manager, workspace_dir: Path, code_understanding_system=None):
        """Initialize the RAG-enhanced system.
        
        Args:
            model_manager: The model manager for AI operations
            workspace_dir: The workspace directory
            code_understanding_system: Optional code understanding system for context
        """
        self.model_manager = model_manager
        self.workspace_dir = workspace_dir
        self.code_understanding = code_understanding_system
        self.lock = threading.RLock()
        
        # Context storage
        self.context_database: Dict[str, RAGContext] = {}
        self.context_index: Dict[str, Set[str]] = {}  # tag -> context_ids
        self.semantic_embeddings: Dict[str, List[float]] = {}
        
        # Generation history
        self.generation_history: List[GenerationResult] = []
        self.context_usage_stats: Dict[str, int] = {}
        
        # Configuration
        self.max_context_items = 10
        self.context_relevance_threshold = 0.3
        self.embedding_cache_size = 1000
        
        # Performance metrics
        self.metrics = {
            'total_generations': 0,
            'successful_generations': 0,
            'average_context_items': 0.0,
            'average_generation_time': 0.0
        }
    
    def generate_with_rag(self, request: GenerationRequest) -> GenerationResult:
        """Generate content using RAG with contextual intelligence.
        
        Args:
            request: Generation request with requirements
            
        Returns:
            Generation result with context and reasoning
        """
        with self.lock:
            start_time = time.time()
            logger.info(f"Starting RAG generation for mode: {request.mode.value}")
            
            try:
                # Retrieve relevant context
                relevant_contexts = self._retrieve_relevant_context(request)
                
                # Rank and filter contexts
                filtered_contexts = self._rank_and_filter_contexts(
                    relevant_contexts, request.max_context_items
                )
                
                # Generate enhanced prompt
                enhanced_prompt = self._create_enhanced_prompt(request, filtered_contexts)
                
                # Generate content with AI model
                generated_content = self._generate_content(enhanced_prompt, request)
                
                # Post-process and validate
                processed_content = self._post_process_content(generated_content, request)
                
                # Calculate confidence score
                confidence_score = self._calculate_confidence_score(
                    processed_content, filtered_contexts, request
                )
                
                # Generate reasoning steps
                reasoning_steps = self._generate_reasoning_steps(
                    request, filtered_contexts, processed_content
                )
                
                # Generate alternatives
                alternatives = self._generate_alternatives(request, filtered_contexts)
                
                execution_time = time.time() - start_time
                
                # Create result
                result = GenerationResult(
                    request_id=request.request_id,
                    generated_content=processed_content,
                    confidence_score=confidence_score,
                    context_used=filtered_contexts,
                    reasoning_steps=reasoning_steps,
                    alternatives=alternatives,
                    execution_time=execution_time,
                    metadata={
                        'mode': request.mode.value,
                        'context_count': len(filtered_contexts),
                        'prompt_length': len(enhanced_prompt)
                    }
                )
                
                # Update metrics and history
                self._update_metrics(result)
                self.generation_history.append(result)
                
                # Update context usage stats
                for context in filtered_contexts:
                    self.context_usage_stats[context.context_id] = \
                        self.context_usage_stats.get(context.context_id, 0) + 1
                
                logger.info(f"RAG generation completed in {execution_time:.2f}s "
                           f"with confidence {confidence_score:.2f}")
                
                return result
                
            except Exception as e:
                logger.error(f"RAG generation failed: {e}", exc_info=True)
                return GenerationResult(
                    request_id=request.request_id,
                    generated_content=f"Generation failed: {str(e)}",
                    confidence_score=0.0,
                    context_used=[],
                    reasoning_steps=[f"Error occurred: {str(e)}"],
                    alternatives=[],
                    execution_time=time.time() - start_time,
                    metadata={'error': str(e)}
                )
    
    def add_context(self, content: str, context_type: ContextType, 
                   metadata: Dict[str, Any] = None, tags: List[str] = None) -> str:
        """Add context to the RAG system.
        
        Args:
            content: Context content
            context_type: Type of context
            metadata: Optional metadata
            tags: Optional tags for indexing
            
        Returns:
            Context ID
        """
        with self.lock:
            context_id = str(uuid.uuid4())
            
            # Calculate relevance score (simplified)
            relevance_score = self._calculate_initial_relevance(content, context_type)
            
            # Create context object
            context = RAGContext(
                context_id=context_id,
                context_type=context_type,
                content=content,
                metadata=metadata or {},
                relevance_score=relevance_score,
                timestamp=time.time(),
                source=metadata.get('source', 'manual') if metadata else 'manual',
                tags=tags or []
            )
            
            # Store context
            self.context_database[context_id] = context
            
            # Update index
            for tag in context.tags:
                if tag not in self.context_index:
                    self.context_index[tag] = set()
                self.context_index[tag].add(context_id)
            
            # Add context type to index
            type_tag = context_type.value
            if type_tag not in self.context_index:
                self.context_index[type_tag] = set()
            self.context_index[type_tag].add(context_id)
            
            logger.debug(f"Added context {context_id} of type {context_type.value}")
            return context_id
    
    def update_context_from_codebase(self):
        """Update context database from the current codebase."""
        if not self.code_understanding:
            logger.warning("No code understanding system available")
            return
        
        with self.lock:
            logger.info("Updating RAG context from codebase...")
            
            # Get all code elements
            for element_id, element in self.code_understanding.code_elements.items():
                # Add function/class context
                if element.element_type.value in ['function', 'class']:
                    context_content = f"""
{element.element_type.value.title()}: {element.name}
Language: {element.language.value}
File: {element.file_path}
Signature: {element.signature}
Documentation: {element.docstring or 'No documentation'}
Parameters: {', '.join(element.parameters)}
Dependencies: {', '.join(element.dependencies)}
Complexity: {element.complexity_score:.2f}
Code:
{element.code_snippet}
"""
                    
                    self.add_context(
                        content=context_content,
                        context_type=ContextType.CODE_CONTEXT,
                        metadata={
                            'element_id': element_id,
                            'element_type': element.element_type.value,
                            'language': element.language.value,
                            'file_path': element.file_path,
                            'complexity': element.complexity_score
                        },
                        tags=[
                            element.language.value,
                            element.element_type.value,
                            element.name.lower()
                        ] + element.semantic_tags
                    )
            
            logger.info(f"Updated RAG context with {len(self.context_database)} items")
    
    def _retrieve_relevant_context(self, request: GenerationRequest) -> List[RAGContext]:
        """Retrieve relevant context for a generation request."""
        relevant_contexts = []
        
        # Extract keywords from prompt
        keywords = self._extract_keywords(request.prompt)
        
        # Search by context type requirements
        for context_type in request.context_requirements:
            type_tag = context_type.value
            if type_tag in self.context_index:
                for context_id in self.context_index[type_tag]:
                    context = self.context_database[context_id]
                    relevance = self._calculate_context_relevance(context, request, keywords)
                    if relevance >= self.context_relevance_threshold:
                        context.relevance_score = relevance
                        relevant_contexts.append(context)
        
        # Search by keywords
        for keyword in keywords:
            if keyword in self.context_index:
                for context_id in self.context_index[keyword]:
                    context = self.context_database[context_id]
                    if context not in relevant_contexts:
                        relevance = self._calculate_context_relevance(context, request, keywords)
                        if relevance >= self.context_relevance_threshold:
                            context.relevance_score = relevance
                            relevant_contexts.append(context)
        
        # Semantic search (simplified)
        semantic_contexts = self._semantic_search(request.prompt, keywords)
        for context in semantic_contexts:
            if context not in relevant_contexts:
                relevant_contexts.append(context)
        
        return relevant_contexts
    
    def _rank_and_filter_contexts(self, contexts: List[RAGContext], max_items: int) -> List[RAGContext]:
        """Rank and filter contexts by relevance."""
        # Sort by relevance score
        sorted_contexts = sorted(contexts, key=lambda x: x.relevance_score, reverse=True)
        
        # Apply diversity filtering to avoid redundant contexts
        filtered_contexts = []
        seen_content_hashes = set()
        
        for context in sorted_contexts:
            if len(filtered_contexts) >= max_items:
                break
            
            # Calculate content hash for deduplication
            content_hash = hashlib.md5(context.content.encode()).hexdigest()
            if content_hash not in seen_content_hashes:
                filtered_contexts.append(context)
                seen_content_hashes.add(content_hash)
        
        return filtered_contexts
    
    def _create_enhanced_prompt(self, request: GenerationRequest, contexts: List[RAGContext]) -> str:
        """Create enhanced prompt with retrieved context."""
        enhanced_prompt = f"""
Based on the following context information, {request.prompt}

CONTEXT INFORMATION:
"""
        
        for i, context in enumerate(contexts, 1):
            enhanced_prompt += f"""
--- Context {i} (Relevance: {context.relevance_score:.2f}) ---
Type: {context.context_type.value}
Source: {context.source}
Content:
{context.content}
"""
        
        enhanced_prompt += f"""

GENERATION REQUIREMENTS:
- Mode: {request.mode.value}
- Constraints: {', '.join(request.constraints)}
- Preferences: {json.dumps(request.preferences, indent=2)}

Please provide a comprehensive response that:
1. Utilizes the provided context effectively
2. Addresses the specific requirements
3. Follows best practices for {request.mode.value}
4. Includes reasoning for your approach
"""
        
        return enhanced_prompt
    
    def _generate_content(self, enhanced_prompt: str, request: GenerationRequest) -> str:
        """Generate content using the AI model."""
        try:
            response = self.model_manager.generate(
                prompt=enhanced_prompt,
                system_prompt=self._get_system_prompt_for_mode(request.mode),
                temperature=request.temperature,
                max_tokens=request.max_tokens
            )
            return response
        except Exception as e:
            logger.error(f"Content generation failed: {e}")
            return f"Generation failed: {str(e)}"
    
    def _get_system_prompt_for_mode(self, mode: RAGMode) -> str:
        """Get system prompt for specific RAG mode."""
        prompts = {
            RAGMode.CODE_GENERATION: """You are an expert code generator. Create high-quality, 
            well-documented code that follows best practices and utilizes the provided context effectively.""",
            
            RAGMode.CODE_COMPLETION: """You are an intelligent code completion assistant. 
            Provide accurate, contextually appropriate code completions based on the surrounding code.""",
            
            RAGMode.PROBLEM_SOLVING: """You are a problem-solving expert. Analyze the problem 
            thoroughly and provide step-by-step solutions using the available context.""",
            
            RAGMode.DEBUGGING: """You are a debugging specialist. Identify issues, explain 
            root causes, and provide comprehensive fixes based on the context.""",
            
            RAGMode.REFACTORING: """You are a refactoring expert. Improve code structure, 
            maintainability, and performance while preserving functionality.""",
            
            RAGMode.DOCUMENTATION: """You are a documentation specialist. Create clear, 
            comprehensive documentation that helps users understand and use the code effectively.""",
            
            RAGMode.TESTING: """You are a testing expert. Design comprehensive test cases 
            that ensure code quality and reliability."""
        }
        
        return prompts.get(mode, "You are an expert AI assistant.")
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text for context retrieval."""
        # Simple keyword extraction (in production, would use NLP libraries)
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Filter out common words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        return list(set(keywords))  # Remove duplicates

    def get_enhanced_response(self, query: str, context: Dict[str, Any] = None) -> str:
        """Get RAG-enhanced response for a query.

        Args:
            query: The query to respond to
            context: Additional context information

        Returns:
            Enhanced response
        """
        try:
            # Create generation request
            request = GenerationRequest(
                prompt=query,
                mode=RAGMode.EXPLANATION,
                context_types=[ContextType.CODE, ContextType.DOCUMENTATION],
                max_context_items=5,
                requirements=context or {},
                constraints=[]
            )

            # Generate with RAG
            result = self.generate_with_rag(request)

            return result.content if result.success else f"Error: {result.content}"

        except Exception as e:
            logger.error(f"Error in get_enhanced_response: {e}")
            return f"Error generating response: {str(e)}"
