#!/usr/bin/env python3
"""
Test script for the enhanced AI coding assistant system.
"""

import sys
import os
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_basic_imports():
    """Test basic imports to identify missing modules."""
    print("Testing basic imports...")
    
    try:
        from models import ModelManager
        print("✓ ModelManager imported successfully")
    except ImportError as e:
        print(f"✗ ModelManager import failed: {e}")
        return False
    
    try:
        from conversation import ConversationManager
        print("✓ ConversationManager imported successfully")
    except ImportError as e:
        print(f"✗ ConversationManager import failed: {e}")
        return False
    
    try:
        from agent import Agent
        print("✓ Agent imported successfully")
    except ImportError as e:
        print(f"✗ Agent import failed: {e}")
        return False
    
    return True

def test_enhanced_agent():
    """Test the enhanced agent functionality."""
    print("\nTesting enhanced agent...")
    
    try:
        from core.iterative_enhanced_agent import IterativeEnhancedAgent
        print("✓ IterativeEnhancedAgent imported successfully")
    except ImportError as e:
        print(f"✗ IterativeEnhancedAgent import failed: {e}")
        return False
    
    try:
        from core.advanced_master_controller import AdvancedMasterController
        print("✓ AdvancedMasterController imported successfully")
    except ImportError as e:
        print(f"✗ AdvancedMasterController import failed: {e}")
        return False
    
    return True

def test_core_modules():
    """Test core module imports."""
    print("\nTesting core modules...")
    
    modules_to_test = [
        "core.self_analyzing_intelligence",
        "core.ai_code_assistant", 
        "core.rag_enhanced_system"
    ]
    
    success_count = 0
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"✓ {module} imported successfully")
            success_count += 1
        except ImportError as e:
            print(f"✗ {module} import failed: {e}")
    
    return success_count == len(modules_to_test)

def test_simple_agent_creation():
    """Test creating a simple agent instance."""
    print("\nTesting agent creation...")
    
    try:
        from models import ModelManager
        from conversation import ConversationManager
        from agent import Agent
        from pathlib import Path
        
        # Create model manager
        model_manager = ModelManager(
            provider="gemini",
            model_name="gemini-2.0-flash",
            temperature=0.7,
            max_tokens=4096
        )
        print("✓ ModelManager created")
        
        # Create conversation manager
        workspace_dir = Path.cwd()
        conversation_manager = ConversationManager(workspace_dir / "conversations")
        print("✓ ConversationManager created")
        
        # Create agent
        agent = Agent(
            model_manager=model_manager,
            conversation_manager=conversation_manager,
            workspace_dir=workspace_dir
        )
        print("✓ Agent created successfully")
        
        # Test basic functionality
        if hasattr(agent, '_has_iterative_agent'):
            print(f"✓ Iterative agent available: {agent._has_iterative_agent}")
        else:
            print("✗ Iterative agent attribute not found")
        
        return True
        
    except Exception as e:
        print(f"✗ Agent creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("Enhanced AI Coding Assistant - System Test")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Enhanced Agent", test_enhanced_agent),
        ("Core Modules", test_core_modules),
        ("Agent Creation", test_simple_agent_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED")
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The enhanced system is ready.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
