"""
Setup script for the Advanced AI Agent.
"""

from setuptools import setup, find_packages
import os
import re

# Read the version from __init__.py
with open("__init__.py", "r") as f:
    version_match = re.search(r'__version__\s*=\s*["\']([^"\']+)["\']', f.read())
    if version_match:
        version = version_match.group(1)
    else:
        version = "0.0.0"

# Read the long description from README.md
long_description = "Advanced AI Agent - A powerful terminal AI coding agent with Gemini API integration."

# Define the required packages
required_packages = [
    "click",
    "prompt_toolkit",
    "rich",
    "requests",
    "google-generativeai",
    "pillow",
    "beautifulsoup4",
]

# Define the optional packages
optional_packages = {
    "rag": ["faiss-cpu", "sentence-transformers"],
}

setup(
    name="advanced_ai_agent",
    version=version,
    description="A powerful terminal AI coding agent with Gemini API integration",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="AI Developer",
    author_email="<EMAIL>",
    url="https://github.com/example/advanced_ai_agent",
    packages=find_packages(),
    py_modules=[
        "agent", "cli", "config", "conversation", "main"
    ],
    entry_points={
        "console_scripts": [
            "advanced-ai-agent=main:main",
        ],
    },
    install_requires=required_packages,
    extras_require=optional_packages,
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.8",
)
