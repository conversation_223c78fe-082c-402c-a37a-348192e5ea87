#!/usr/bin/env python3
"""
Test script for the Autonomous AI Agent System.

This script demonstrates the autonomous agent capabilities including:
1. Task persistence and completion tracking
2. Autonomous research and information gathering
3. Multi-step workflow execution
4. Progress monitoring and reporting
5. Iterative improvement and self-correction
"""

import sys
import os
import time
import json
from pathlib import Path
from typing import Dict, Any, List

# Add the current directory to the path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from models import ModelManager
from conversation import ConversationManager
from agent import Agent
from config import load_config
from utils import get_logger

logger = get_logger()

class AutonomousAgentTester:
    """Test harness for autonomous agent capabilities."""
    
    def __init__(self):
        """Initialize the test harness."""
        self.config = load_config()
        self.workspace_dir = Path(self.config.workspace_dir)
        
        # Initialize components
        self.model_manager = ModelManager(
            provider=self.config.agent.provider,
            model_name=self.config.agent.model,
            temperature=self.config.agent.temperature,
            max_tokens=self.config.agent.max_tokens
        )
        
        self.conversation_manager = ConversationManager(self.config.history_dir)
        
        self.agent = Agent(
            model_manager=self.model_manager,
            conversation_manager=self.conversation_manager,
            workspace_dir=self.workspace_dir
        )
        
        # Test results
        self.test_results = []
        self.current_session_id = None
        
        print("🤖 Autonomous Agent Test Harness Initialized")
        print(f"Workspace: {self.workspace_dir}")
        print(f"Model: {self.config.agent.provider}/{self.config.agent.model}")
        print()
    
    def run_all_tests(self):
        """Run all autonomous agent tests."""
        print("🚀 Starting Autonomous Agent Tests")
        print("=" * 50)
        
        try:
            # Test 1: Basic autonomous mode functionality
            self.test_basic_autonomous_mode()
            
            # Test 2: Task persistence and recovery
            self.test_task_persistence()
            
            # Test 3: Research capabilities
            self.test_research_capabilities()
            
            # Test 4: Multi-step workflow execution
            self.test_workflow_execution()
            
            # Test 5: Progress monitoring
            self.test_progress_monitoring()
            
            # Test 6: Error handling and recovery
            self.test_error_handling()
            
            # Test 7: Semi-autonomous mode
            self.test_semi_autonomous_mode()
            
            # Print test summary
            self.print_test_summary()
            
        except Exception as e:
            logger.error(f"Test execution failed: {e}")
            print(f"❌ Test execution failed: {e}")
    
    def test_basic_autonomous_mode(self):
        """Test basic autonomous mode functionality."""
        print("🧪 Test 1: Basic Autonomous Mode")
        print("-" * 30)
        
        test_task = "Create a simple Python function that calculates the factorial of a number"
        
        try:
            # Check if autonomous controller is available
            status = self.agent.get_autonomous_status()
            if not status.get('available', False):
                self.record_test_result("Basic Autonomous Mode", False, "Autonomous controller not available")
                return
            
            # Set up callbacks
            progress_updates = []
            status_updates = []
            
            def progress_callback(data):
                progress_updates.append(data)
                print(f"  📈 Progress: {data.get('task_title', 'Unknown')} - {data.get('progress', 0)*100:.1f}%")
            
            def status_callback(data):
                status_updates.append(data)
                print(f"  📊 Status: {data.get('type', 'Unknown')}")
            
            self.agent.set_autonomous_callbacks(
                progress_callback=progress_callback,
                status_callback=status_callback
            )
            
            # Start autonomous session
            session_id = self.agent.start_autonomous_mode(test_task, "autonomous")
            
            if session_id:
                self.current_session_id = session_id
                print(f"  ✅ Session started: {session_id}")
                
                # Monitor for a short time
                start_time = time.time()
                timeout = 30  # 30 seconds timeout for test
                
                while time.time() - start_time < timeout:
                    status = self.agent.get_autonomous_status()
                    if not status.get('active', False):
                        print("  🏁 Session completed")
                        break
                    time.sleep(2)
                
                # Stop the session
                self.agent.stop_autonomous_mode()
                
                self.record_test_result(
                    "Basic Autonomous Mode", 
                    True, 
                    f"Session created and monitored. Progress updates: {len(progress_updates)}"
                )
            else:
                self.record_test_result("Basic Autonomous Mode", False, "Failed to start session")
                
        except Exception as e:
            self.record_test_result("Basic Autonomous Mode", False, str(e))
        
        print()
    
    def test_task_persistence(self):
        """Test task persistence and recovery."""
        print("🧪 Test 2: Task Persistence")
        print("-" * 30)
        
        try:
            # Get current autonomous status
            status = self.agent.get_autonomous_status()
            
            if status.get('active_tasks_count', 0) > 0:
                print(f"  📋 Found {status['active_tasks_count']} active tasks")
                
                # Try to get details of first active task
                active_tasks = status.get('active_tasks', [])
                if active_tasks:
                    task_id = active_tasks[0]['task_id']
                    task_details = self.agent.get_autonomous_task_details(task_id)
                    
                    if task_details:
                        print(f"  📄 Task details retrieved: {task_details['title']}")
                        print(f"  📊 Progress: {task_details['progress']*100:.1f}%")
                        print(f"  📝 Next steps: {len(task_details['next_steps'])}")
                        
                        self.record_test_result("Task Persistence", True, "Task details retrieved successfully")
                    else:
                        self.record_test_result("Task Persistence", False, "Could not retrieve task details")
                else:
                    self.record_test_result("Task Persistence", True, "No active tasks to test")
            else:
                print("  ℹ️ No active tasks found")
                self.record_test_result("Task Persistence", True, "No active tasks to test")
                
        except Exception as e:
            self.record_test_result("Task Persistence", False, str(e))
        
        print()
    
    def test_research_capabilities(self):
        """Test autonomous research capabilities."""
        print("🧪 Test 3: Research Capabilities")
        print("-" * 30)
        
        try:
            # Get research history
            research_history = self.agent.get_autonomous_research_history()
            
            print(f"  🔍 Research history entries: {len(research_history)}")
            
            if research_history:
                latest_research = research_history[-1]
                print(f"  📚 Latest research: {latest_research.get('query', 'Unknown')}")
                print(f"  📊 Confidence: {latest_research.get('confidence', 0):.2f}")
                print(f"  📄 Sources: {latest_research.get('sources_count', 0)}")
                
                self.record_test_result("Research Capabilities", True, f"Found {len(research_history)} research entries")
            else:
                print("  ℹ️ No research history found")
                self.record_test_result("Research Capabilities", True, "No research history to test")
                
        except Exception as e:
            self.record_test_result("Research Capabilities", False, str(e))
        
        print()
    
    def test_workflow_execution(self):
        """Test multi-step workflow execution."""
        print("🧪 Test 4: Workflow Execution")
        print("-" * 30)
        
        try:
            # Start a complex task that requires multiple steps
            complex_task = "Research Python web frameworks, compare Flask and Django, and create a simple example application"
            
            session_id = self.agent.start_autonomous_mode(complex_task, "autonomous")
            
            if session_id:
                print(f"  🔄 Complex workflow started: {session_id}")
                
                # Monitor for workflow steps
                step_count = 0
                start_time = time.time()
                timeout = 45  # 45 seconds for complex task
                
                while time.time() - start_time < timeout:
                    status = self.agent.get_autonomous_status()
                    
                    if not status.get('active', False):
                        print("  🏁 Workflow completed")
                        break
                    
                    current_tasks = status.get('active_tasks', [])
                    if current_tasks:
                        current_task = current_tasks[0]
                        if len(current_task.get('next_steps', [])) != step_count:
                            step_count = len(current_task.get('next_steps', []))
                            print(f"  📋 Workflow steps: {step_count}")
                    
                    time.sleep(3)
                
                self.agent.stop_autonomous_mode()
                
                self.record_test_result("Workflow Execution", True, f"Complex workflow executed with {step_count} steps")
            else:
                self.record_test_result("Workflow Execution", False, "Failed to start complex workflow")
                
        except Exception as e:
            self.record_test_result("Workflow Execution", False, str(e))
        
        print()
    
    def test_progress_monitoring(self):
        """Test progress monitoring capabilities."""
        print("🧪 Test 5: Progress Monitoring")
        print("-" * 30)
        
        try:
            # Get session history
            session_history = self.agent.get_autonomous_session_history()
            
            print(f"  📊 Session history entries: {len(session_history)}")
            
            if session_history:
                latest_session = session_history[-1]
                print(f"  🆔 Latest session: {latest_session.get('session_id', 'Unknown')}")
                print(f"  📈 Tasks completed: {latest_session.get('tasks_completed', 0)}")
                print(f"  🔬 Research conducted: {latest_session.get('research_conducted', 0)}")
                
                self.record_test_result("Progress Monitoring", True, f"Monitored {len(session_history)} sessions")
            else:
                print("  ℹ️ No session history found")
                self.record_test_result("Progress Monitoring", True, "No session history to monitor")
                
        except Exception as e:
            self.record_test_result("Progress Monitoring", False, str(e))
        
        print()
    
    def test_error_handling(self):
        """Test error handling and recovery."""
        print("🧪 Test 6: Error Handling")
        print("-" * 30)
        
        try:
            # Test pause/resume functionality
            test_task = "Create a test task for error handling"
            session_id = self.agent.start_autonomous_mode(test_task, "autonomous")
            
            if session_id:
                print("  ⏸️ Testing pause functionality...")
                pause_result = self.agent.pause_autonomous_task()
                print(f"  Pause result: {pause_result}")
                
                time.sleep(2)
                
                print("  ▶️ Testing resume functionality...")
                resume_result = self.agent.resume_autonomous_task()
                print(f"  Resume result: {resume_result}")
                
                self.agent.stop_autonomous_mode()
                
                self.record_test_result("Error Handling", True, f"Pause: {pause_result}, Resume: {resume_result}")
            else:
                self.record_test_result("Error Handling", False, "Could not start test session")
                
        except Exception as e:
            self.record_test_result("Error Handling", False, str(e))
        
        print()
    
    def test_semi_autonomous_mode(self):
        """Test semi-autonomous mode."""
        print("🧪 Test 7: Semi-Autonomous Mode")
        print("-" * 30)
        
        try:
            # Test semi-autonomous mode availability
            test_task = "Test semi-autonomous mode functionality"
            session_id = self.agent.start_autonomous_mode(test_task, "semi_autonomous")
            
            if session_id:
                print(f"  🤝 Semi-autonomous session started: {session_id}")
                
                # Monitor briefly
                time.sleep(5)
                
                status = self.agent.get_autonomous_status()
                print(f"  📊 Mode: {status.get('mode', 'Unknown')}")
                
                self.agent.stop_autonomous_mode()
                
                self.record_test_result("Semi-Autonomous Mode", True, "Semi-autonomous mode tested")
            else:
                self.record_test_result("Semi-Autonomous Mode", False, "Could not start semi-autonomous session")
                
        except Exception as e:
            self.record_test_result("Semi-Autonomous Mode", False, str(e))
        
        print()
    
    def record_test_result(self, test_name: str, success: bool, details: str):
        """Record a test result."""
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": time.time()
        })
    
    def print_test_summary(self):
        """Print test summary."""
        print("📋 Test Summary")
        print("=" * 50)
        
        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)
        
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        print()
        
        for result in self.test_results:
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            print(f"{status} {result['test']}")
            print(f"    {result['details']}")
            print()
        
        # Save results to file
        results_file = self.workspace_dir / "test_results.json"
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"📄 Detailed results saved to: {results_file}")

def main():
    """Main test function."""
    print("🤖 Autonomous AI Agent System Test")
    print("=" * 50)
    print()
    
    try:
        tester = AutonomousAgentTester()
        tester.run_all_tests()
        
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
    except Exception as e:
        print(f"❌ Test harness failed: {e}")
        logger.error(f"Test harness failed: {e}")

if __name__ == "__main__":
    main()
