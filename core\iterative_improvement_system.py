"""
Iterative Improvement System for Autonomous AI Agent.

This system provides:
1. Continuous testing and validation of agent outputs
2. Self-correction capabilities based on feedback
3. Learning from failures and successes
4. Quality improvement through iteration
5. Confidence-based decision making
"""

import time
import logging
import json
import uuid
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum
from datetime import datetime

logger = logging.getLogger(__name__)

class ImprovementType(Enum):
    """Types of improvements the system can make."""
    CODE_QUALITY = "code_quality"
    LOGIC_CORRECTION = "logic_correction"
    PERFORMANCE_OPTIMIZATION = "performance_optimization"
    ERROR_HANDLING = "error_handling"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    SECURITY = "security"
    USABILITY = "usability"

class ValidationResult(Enum):
    """Results of validation checks."""
    PASS = "pass"
    FAIL = "fail"
    WARNING = "warning"
    NEEDS_IMPROVEMENT = "needs_improvement"

@dataclass
class TestCase:
    """Represents a test case for validation."""
    test_id: str
    name: str
    description: str
    test_type: str  # unit, integration, functional, etc.
    input_data: Dict[str, Any]
    expected_output: Any
    validation_criteria: List[str]
    timeout: float = 30.0
    created_at: datetime = field(default_factory=datetime.now)

@dataclass
class ValidationReport:
    """Report from validation process."""
    validation_id: str
    test_case: TestCase
    result: ValidationResult
    actual_output: Any
    execution_time: float
    confidence_score: float
    issues_found: List[str]
    suggestions: List[str]
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class ImprovementSuggestion:
    """Suggestion for improvement."""
    suggestion_id: str
    improvement_type: ImprovementType
    priority: int  # 1-10, 10 being highest
    description: str
    proposed_changes: str
    expected_benefit: str
    implementation_effort: str  # low, medium, high
    confidence: float
    supporting_evidence: List[str]
    created_at: datetime = field(default_factory=datetime.now)

@dataclass
class IterationResult:
    """Result of an improvement iteration."""
    iteration_id: str
    original_content: str
    improved_content: str
    improvements_applied: List[ImprovementSuggestion]
    validation_reports: List[ValidationReport]
    overall_improvement_score: float
    confidence_increase: float
    execution_time: float
    success: bool
    metadata: Dict[str, Any] = field(default_factory=dict)

class IterativeImprovementSystem:
    """System for iterative improvement of agent outputs."""
    
    def __init__(self, model_manager, tools: Dict[str, Any], workspace_dir: Path):
        """Initialize the iterative improvement system."""
        self.model_manager = model_manager
        self.tools = tools
        self.workspace_dir = workspace_dir
        
        # Configuration
        self.max_iterations = 5
        self.confidence_threshold = 0.8
        self.improvement_threshold = 0.1
        self.timeout_per_iteration = 120.0
        
        # State tracking
        self.iteration_history: List[IterationResult] = []
        self.test_cases: Dict[str, TestCase] = {}
        self.improvement_patterns: Dict[str, List[ImprovementSuggestion]] = {}
        
        # Validation functions
        self.validators: Dict[str, Callable] = {
            "code_syntax": self._validate_code_syntax,
            "code_execution": self._validate_code_execution,
            "logic_correctness": self._validate_logic_correctness,
            "performance": self._validate_performance,
            "security": self._validate_security,
            "documentation": self._validate_documentation
        }
        
        logger.info("Iterative improvement system initialized")
    
    def improve_iteratively(self, 
                          content: str,
                          content_type: str,
                          success_criteria: List[str],
                          context: Dict[str, Any] = None) -> IterationResult:
        """Improve content through iterative refinement."""
        logger.info(f"Starting iterative improvement for {content_type}")
        
        current_content = content
        iteration_count = 0
        all_improvements = []
        all_validations = []
        initial_confidence = self._calculate_initial_confidence(content, content_type)
        
        start_time = time.time()
        
        while iteration_count < self.max_iterations:
            iteration_count += 1
            logger.info(f"Starting iteration {iteration_count}")
            
            # Validate current content
            validation_reports = self._validate_content(
                current_content, content_type, success_criteria, context
            )
            all_validations.extend(validation_reports)
            
            # Calculate current confidence
            current_confidence = self._calculate_confidence_from_validations(validation_reports)
            
            # Check if we've reached acceptable quality
            if current_confidence >= self.confidence_threshold:
                logger.info(f"Confidence threshold reached: {current_confidence:.2f}")
                break
            
            # Generate improvement suggestions
            suggestions = self._generate_improvement_suggestions(
                current_content, validation_reports, content_type, context
            )
            
            if not suggestions:
                logger.info("No improvement suggestions generated")
                break
            
            # Apply improvements
            improved_content = self._apply_improvements(current_content, suggestions)
            
            # Validate improvements
            improvement_validations = self._validate_content(
                improved_content, content_type, success_criteria, context
            )
            
            new_confidence = self._calculate_confidence_from_validations(improvement_validations)
            improvement_score = new_confidence - current_confidence
            
            # Check if improvement is significant
            if improvement_score < self.improvement_threshold:
                logger.info(f"Improvement too small: {improvement_score:.3f}")
                break
            
            # Accept improvements
            current_content = improved_content
            all_improvements.extend(suggestions)
            all_validations.extend(improvement_validations)
            
            logger.info(f"Iteration {iteration_count} completed. Confidence: {new_confidence:.2f}")
        
        # Create final result
        final_confidence = self._calculate_confidence_from_validations(all_validations[-len(self.validators):])
        
        result = IterationResult(
            iteration_id=str(uuid.uuid4()),
            original_content=content,
            improved_content=current_content,
            improvements_applied=all_improvements,
            validation_reports=all_validations,
            overall_improvement_score=final_confidence - initial_confidence,
            confidence_increase=final_confidence - initial_confidence,
            execution_time=time.time() - start_time,
            success=final_confidence >= self.confidence_threshold,
            metadata={
                "iterations_performed": iteration_count,
                "content_type": content_type,
                "success_criteria": success_criteria,
                "initial_confidence": initial_confidence,
                "final_confidence": final_confidence
            }
        )
        
        self.iteration_history.append(result)
        return result
    
    def _validate_content(self, 
                         content: str, 
                         content_type: str, 
                         success_criteria: List[str],
                         context: Dict[str, Any] = None) -> List[ValidationReport]:
        """Validate content using appropriate validators."""
        reports = []
        
        # Determine which validators to use based on content type
        applicable_validators = self._get_applicable_validators(content_type)
        
        for validator_name in applicable_validators:
            if validator_name in self.validators:
                try:
                    validator_func = self.validators[validator_name]
                    report = validator_func(content, success_criteria, context or {})
                    reports.append(report)
                except Exception as e:
                    logger.error(f"Validation error with {validator_name}: {e}")
                    # Create error report
                    error_report = ValidationReport(
                        validation_id=str(uuid.uuid4()),
                        test_case=TestCase(
                            test_id=str(uuid.uuid4()),
                            name=f"{validator_name}_error",
                            description=f"Error in {validator_name} validation",
                            test_type="error",
                            input_data={"content": content[:100]},
                            expected_output="successful_validation",
                            validation_criteria=[]
                        ),
                        result=ValidationResult.FAIL,
                        actual_output=str(e),
                        execution_time=0.0,
                        confidence_score=0.0,
                        issues_found=[f"Validation error: {e}"],
                        suggestions=[f"Fix {validator_name} validation issue"]
                    )
                    reports.append(error_report)
        
        return reports
    
    def _get_applicable_validators(self, content_type: str) -> List[str]:
        """Get list of applicable validators for content type."""
        validator_map = {
            "code": ["code_syntax", "code_execution", "logic_correctness", "performance", "security"],
            "python": ["code_syntax", "code_execution", "logic_correctness", "performance", "security"],
            "javascript": ["code_syntax", "logic_correctness", "security"],
            "documentation": ["documentation"],
            "text": ["logic_correctness", "documentation"],
            "analysis": ["logic_correctness"],
            "plan": ["logic_correctness"]
        }
        
        return validator_map.get(content_type, ["logic_correctness"])
    
    def _validate_code_syntax(self, content: str, criteria: List[str], context: Dict[str, Any]) -> ValidationReport:
        """Validate code syntax."""
        test_case = TestCase(
            test_id=str(uuid.uuid4()),
            name="code_syntax_check",
            description="Check if code has valid syntax",
            test_type="syntax",
            input_data={"code": content},
            expected_output="valid_syntax",
            validation_criteria=["syntactically_correct", "parseable"]
        )
        
        issues = []
        suggestions = []
        confidence = 1.0
        
        try:
            # Try to parse Python code
            if "python" in context.get("language", "").lower() or "def " in content or "import " in content:
                compile(content, '<string>', 'exec')
                result = ValidationResult.PASS
            else:
                # For other languages, do basic checks
                if content.strip():
                    result = ValidationResult.PASS
                else:
                    result = ValidationResult.FAIL
                    issues.append("Empty content")
                    confidence = 0.0
                    
        except SyntaxError as e:
            result = ValidationResult.FAIL
            issues.append(f"Syntax error: {e}")
            suggestions.append("Fix syntax errors in the code")
            confidence = 0.2
        except Exception as e:
            result = ValidationResult.WARNING
            issues.append(f"Could not validate syntax: {e}")
            confidence = 0.5
        
        return ValidationReport(
            validation_id=str(uuid.uuid4()),
            test_case=test_case,
            result=result,
            actual_output=content,
            execution_time=0.1,
            confidence_score=confidence,
            issues_found=issues,
            suggestions=suggestions
        )
    
    def _validate_code_execution(self, content: str, criteria: List[str], context: Dict[str, Any]) -> ValidationReport:
        """Validate code execution."""
        test_case = TestCase(
            test_id=str(uuid.uuid4()),
            name="code_execution_check",
            description="Check if code executes without errors",
            test_type="execution",
            input_data={"code": content},
            expected_output="successful_execution",
            validation_criteria=["executes_without_error", "produces_output"]
        )
        
        issues = []
        suggestions = []
        confidence = 0.5
        result = ValidationResult.WARNING
        
        try:
            # Use code tool if available
            if "code" in self.tools:
                execution_result = self.tools["code"].execute(content)
                if "error" not in str(execution_result).lower():
                    result = ValidationResult.PASS
                    confidence = 0.9
                else:
                    result = ValidationResult.FAIL
                    issues.append(f"Execution error: {execution_result}")
                    suggestions.append("Fix runtime errors in the code")
                    confidence = 0.3
            else:
                # Basic static analysis
                if "def " in content and "return" in content:
                    result = ValidationResult.PASS
                    confidence = 0.7
                
        except Exception as e:
            issues.append(f"Execution validation error: {e}")
            suggestions.append("Review code for execution issues")
            confidence = 0.2
        
        return ValidationReport(
            validation_id=str(uuid.uuid4()),
            test_case=test_case,
            result=result,
            actual_output=content,
            execution_time=1.0,
            confidence_score=confidence,
            issues_found=issues,
            suggestions=suggestions
        )
    
    def _validate_logic_correctness(self, content: str, criteria: List[str], context: Dict[str, Any]) -> ValidationReport:
        """Validate logical correctness."""
        test_case = TestCase(
            test_id=str(uuid.uuid4()),
            name="logic_correctness_check",
            description="Check logical correctness of content",
            test_type="logic",
            input_data={"content": content},
            expected_output="logically_correct",
            validation_criteria=criteria
        )
        
        issues = []
        suggestions = []
        confidence = 0.7  # Default confidence for logic
        
        # Use AI model to assess logic
        logic_prompt = f"""
        Analyze the following content for logical correctness:
        
        Content: {content}
        
        Success Criteria: {criteria}
        
        Assess:
        1. Does the content meet the success criteria?
        2. Are there any logical inconsistencies?
        3. Is the approach sound?
        4. What improvements could be made?
        
        Respond with a score from 0.0 to 1.0 and brief explanation.
        """
        
        try:
            response = self.model_manager.generate(prompt=logic_prompt)
            
            # Parse response for score and issues
            if "0." in response or "1." in response:
                # Extract score
                import re
                score_match = re.search(r'([0-1]\.\d+)', response)
                if score_match:
                    confidence = float(score_match.group(1))
            
            if confidence >= 0.8:
                result = ValidationResult.PASS
            elif confidence >= 0.6:
                result = ValidationResult.WARNING
                suggestions.append("Consider improving logical structure")
            else:
                result = ValidationResult.NEEDS_IMPROVEMENT
                issues.append("Logic needs improvement")
                suggestions.append("Revise logical approach")
                
        except Exception as e:
            logger.error(f"Logic validation error: {e}")
            result = ValidationResult.WARNING
            confidence = 0.5
        
        return ValidationReport(
            validation_id=str(uuid.uuid4()),
            test_case=test_case,
            result=result,
            actual_output=content,
            execution_time=2.0,
            confidence_score=confidence,
            issues_found=issues,
            suggestions=suggestions
        )
    
    def _validate_performance(self, content: str, criteria: List[str], context: Dict[str, Any]) -> ValidationReport:
        """Validate performance characteristics."""
        test_case = TestCase(
            test_id=str(uuid.uuid4()),
            name="performance_check",
            description="Check performance characteristics",
            test_type="performance",
            input_data={"content": content},
            expected_output="acceptable_performance",
            validation_criteria=["efficient", "scalable"]
        )
        
        issues = []
        suggestions = []
        confidence = 0.7
        result = ValidationResult.PASS
        
        # Basic performance analysis
        if len(content) > 10000:
            issues.append("Content is very long")
            suggestions.append("Consider breaking into smaller parts")
            confidence = 0.6
        
        # Check for obvious performance issues in code
        if "while True:" in content and "break" not in content:
            issues.append("Potential infinite loop detected")
            suggestions.append("Add proper loop termination conditions")
            confidence = 0.3
            result = ValidationResult.FAIL
        
        return ValidationReport(
            validation_id=str(uuid.uuid4()),
            test_case=test_case,
            result=result,
            actual_output=content,
            execution_time=0.5,
            confidence_score=confidence,
            issues_found=issues,
            suggestions=suggestions
        )
    
    def _validate_security(self, content: str, criteria: List[str], context: Dict[str, Any]) -> ValidationReport:
        """Validate security aspects."""
        test_case = TestCase(
            test_id=str(uuid.uuid4()),
            name="security_check",
            description="Check for security issues",
            test_type="security",
            input_data={"content": content},
            expected_output="secure_code",
            validation_criteria=["no_vulnerabilities", "safe_practices"]
        )
        
        issues = []
        suggestions = []
        confidence = 0.8
        result = ValidationResult.PASS
        
        # Basic security checks
        security_risks = [
            ("eval(", "Avoid using eval()"),
            ("exec(", "Avoid using exec()"),
            ("os.system(", "Use subprocess instead of os.system()"),
            ("shell=True", "Avoid shell=True in subprocess calls"),
            ("input(", "Validate user input properly")
        ]
        
        for risk, suggestion in security_risks:
            if risk in content:
                issues.append(f"Security risk: {risk}")
                suggestions.append(suggestion)
                confidence -= 0.2
        
        if confidence < 0.6:
            result = ValidationResult.FAIL
        elif confidence < 0.8:
            result = ValidationResult.WARNING
        
        return ValidationReport(
            validation_id=str(uuid.uuid4()),
            test_case=test_case,
            result=result,
            actual_output=content,
            execution_time=0.3,
            confidence_score=max(confidence, 0.0),
            issues_found=issues,
            suggestions=suggestions
        )
    
    def _validate_documentation(self, content: str, criteria: List[str], context: Dict[str, Any]) -> ValidationReport:
        """Validate documentation quality."""
        test_case = TestCase(
            test_id=str(uuid.uuid4()),
            name="documentation_check",
            description="Check documentation quality",
            test_type="documentation",
            input_data={"content": content},
            expected_output="well_documented",
            validation_criteria=["clear", "complete", "helpful"]
        )
        
        issues = []
        suggestions = []
        confidence = 0.5
        
        # Check for documentation elements
        doc_elements = 0
        
        if '"""' in content or "'''" in content:
            doc_elements += 1
        if "def " in content and ("Args:" in content or "Parameters:" in content):
            doc_elements += 1
        if "Returns:" in content or "return" in content:
            doc_elements += 1
        if "#" in content:  # Comments
            doc_elements += 1
        
        confidence = min(doc_elements * 0.25, 1.0)
        
        if confidence >= 0.8:
            result = ValidationResult.PASS
        elif confidence >= 0.5:
            result = ValidationResult.WARNING
            suggestions.append("Add more comprehensive documentation")
        else:
            result = ValidationResult.NEEDS_IMPROVEMENT
            issues.append("Insufficient documentation")
            suggestions.append("Add docstrings, comments, and usage examples")
        
        return ValidationReport(
            validation_id=str(uuid.uuid4()),
            test_case=test_case,
            result=result,
            actual_output=content,
            execution_time=0.2,
            confidence_score=confidence,
            issues_found=issues,
            suggestions=suggestions
        )

    def _calculate_initial_confidence(self, content: str, content_type: str) -> float:
        """Calculate initial confidence score for content."""
        if not content.strip():
            return 0.0

        # Basic heuristics for initial confidence
        base_confidence = 0.5

        if content_type == "code":
            if "def " in content:
                base_confidence += 0.2
            if "import " in content:
                base_confidence += 0.1
            if len(content) > 100:
                base_confidence += 0.1

        return min(base_confidence, 1.0)

    def _calculate_confidence_from_validations(self, validations: List[ValidationReport]) -> float:
        """Calculate overall confidence from validation reports."""
        if not validations:
            return 0.0

        total_confidence = sum(v.confidence_score for v in validations)
        return total_confidence / len(validations)

    def _generate_improvement_suggestions(self,
                                        content: str,
                                        validations: List[ValidationReport],
                                        content_type: str,
                                        context: Dict[str, Any]) -> List[ImprovementSuggestion]:
        """Generate improvement suggestions based on validation results."""
        suggestions = []

        # Collect all issues and suggestions from validations
        all_issues = []
        all_suggestions = []

        for validation in validations:
            all_issues.extend(validation.issues_found)
            all_suggestions.extend(validation.suggestions)

        # Generate improvement suggestions
        improvement_prompt = f"""
        Based on the following analysis, suggest specific improvements:

        Content Type: {content_type}
        Content: {content[:500]}...

        Issues Found: {all_issues}
        Suggestions: {all_suggestions}

        Provide 3-5 specific, actionable improvement suggestions with:
        1. Type of improvement
        2. Priority (1-10)
        3. Description
        4. Proposed changes
        5. Expected benefit
        """

        try:
            response = self.model_manager.generate(prompt=improvement_prompt)

            # Parse response and create suggestions
            # For now, create basic suggestions based on validation results
            for i, suggestion_text in enumerate(all_suggestions[:5]):
                suggestion = ImprovementSuggestion(
                    suggestion_id=str(uuid.uuid4()),
                    improvement_type=self._classify_improvement_type(suggestion_text),
                    priority=max(8 - i, 1),  # Higher priority for first suggestions
                    description=suggestion_text,
                    proposed_changes=f"Apply suggestion: {suggestion_text}",
                    expected_benefit="Improved quality and reliability",
                    implementation_effort="medium",
                    confidence=0.7,
                    supporting_evidence=[f"Validation issue: {issue}" for issue in all_issues[:3]]
                )
                suggestions.append(suggestion)

        except Exception as e:
            logger.error(f"Error generating improvement suggestions: {e}")

        return suggestions

    def _classify_improvement_type(self, suggestion_text: str) -> ImprovementType:
        """Classify the type of improvement based on suggestion text."""
        text_lower = suggestion_text.lower()

        if "syntax" in text_lower or "error" in text_lower:
            return ImprovementType.CODE_QUALITY
        elif "logic" in text_lower or "approach" in text_lower:
            return ImprovementType.LOGIC_CORRECTION
        elif "performance" in text_lower or "efficient" in text_lower:
            return ImprovementType.PERFORMANCE_OPTIMIZATION
        elif "security" in text_lower or "safe" in text_lower:
            return ImprovementType.SECURITY
        elif "document" in text_lower or "comment" in text_lower:
            return ImprovementType.DOCUMENTATION
        elif "test" in text_lower:
            return ImprovementType.TESTING
        else:
            return ImprovementType.CODE_QUALITY

    def _apply_improvements(self, content: str, suggestions: List[ImprovementSuggestion]) -> str:
        """Apply improvement suggestions to content."""
        improved_content = content

        # Sort suggestions by priority
        sorted_suggestions = sorted(suggestions, key=lambda s: s.priority, reverse=True)

        # Apply improvements using AI model
        for suggestion in sorted_suggestions[:3]:  # Apply top 3 suggestions
            improvement_prompt = f"""
            Apply this improvement to the content:

            Original Content:
            {improved_content}

            Improvement: {suggestion.description}
            Type: {suggestion.improvement_type.value}
            Proposed Changes: {suggestion.proposed_changes}

            Provide the improved version of the content.
            """

            try:
                response = self.model_manager.generate(prompt=improvement_prompt)

                # Extract improved content from response
                if len(response) > len(improved_content) * 0.5:  # Sanity check
                    improved_content = response
                    logger.info(f"Applied improvement: {suggestion.improvement_type.value}")

            except Exception as e:
                logger.error(f"Error applying improvement {suggestion.suggestion_id}: {e}")

        return improved_content

    def get_improvement_history(self) -> List[IterationResult]:
        """Get history of improvement iterations."""
        return self.iteration_history.copy()

    def get_improvement_statistics(self) -> Dict[str, Any]:
        """Get statistics about improvement performance."""
        if not self.iteration_history:
            return {"total_iterations": 0}

        total_iterations = len(self.iteration_history)
        successful_iterations = sum(1 for r in self.iteration_history if r.success)

        avg_improvement = sum(r.overall_improvement_score for r in self.iteration_history) / total_iterations
        avg_confidence_increase = sum(r.confidence_increase for r in self.iteration_history) / total_iterations
        avg_execution_time = sum(r.execution_time for r in self.iteration_history) / total_iterations

        improvement_types = {}
        for result in self.iteration_history:
            for improvement in result.improvements_applied:
                imp_type = improvement.improvement_type.value
                improvement_types[imp_type] = improvement_types.get(imp_type, 0) + 1

        return {
            "total_iterations": total_iterations,
            "successful_iterations": successful_iterations,
            "success_rate": successful_iterations / total_iterations,
            "average_improvement_score": avg_improvement,
            "average_confidence_increase": avg_confidence_increase,
            "average_execution_time": avg_execution_time,
            "improvement_types_applied": improvement_types,
            "most_common_improvement": max(improvement_types.items(), key=lambda x: x[1])[0] if improvement_types else None
        }
