"""
Model manager for the Advanced AI Agent.
"""

from typing import Dict, List, Optional, Any, Generator, Union

from PIL import Image

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from models.gemini import GeminiModel
from config import get_api_key, get_available_models

class ModelManager:
    """Model manager for the Advanced AI Agent."""

    def __init__(
        self,
        provider: str = "gemini",
        model_name: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 4096
    ):
        """Initialize the model manager.

        Args:
            provider: The provider to use.
            model_name: The name of the model to use. If None, will use the default for the provider.
            temperature: The temperature to use for generation.
            max_tokens: The maximum number of tokens to generate.
        """
        self.provider = provider
        self.temperature = temperature
        self.max_tokens = max_tokens

        # Get available models
        self.available_models = get_available_models()

        # Set default model name if not provided
        if model_name is None:
            if provider in self.available_models and self.available_models[provider]:
                model_name = self.available_models[provider][0]
            else:
                raise ValueError(f"No models available for provider {provider}")

        self.model_name = model_name

        # Initialize the model based on the provider
        if provider == "gemini":
            self.model = GeminiModel(
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
                api_key=get_api_key("gemini")
            )
        elif provider == "test":
            # Use dedicated test model
            from .test_model import TestModel
            self.model = TestModel(
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
        else:
            # For unknown providers, create a mock model
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Unknown provider: {provider}, using test model for compatibility")
            from .test_model import TestModel
            self.model = TestModel(
                model_name=f"mock-{model_name}",
                temperature=temperature,
                max_tokens=max_tokens
            )

    def generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        images: Optional[List[Union[str, Image.Image]]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Generate a response to a prompt.

        Args:
            prompt: The prompt to generate a response to.
            system_prompt: The system prompt to use.
            images: A list of images to include in the prompt.
            conversation_history: Previous conversation messages.

        Returns:
            The generated response.
        """
        return self.model.generate(prompt, system_prompt, images, conversation_history)

    def chat_generate(
        self,
        message: str,
        images: Optional[List[Union[str, Image.Image]]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Generate a response in a chat session.

        Args:
            message: The message to generate a response to.
            images: A list of images to include in the message.
            conversation_history: Previous conversation messages.

        Returns:
            The generated response.
        """
        return self.model.chat_generate(message, images, conversation_history)

    def stream_generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        images: Optional[List[Union[str, Image.Image]]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None
    ) -> Generator[str, None, None]:
        """Stream a response to a prompt.

        Args:
            prompt: The prompt to generate a response to.
            system_prompt: The system prompt to use.
            images: A list of images to include in the prompt.
            conversation_history: Previous conversation messages.

        Yields:
            Chunks of the generated response.
        """
        yield from self.model.stream_generate(prompt, system_prompt, images, conversation_history)

    def stream_chat_generate(
        self,
        message: str,
        images: Optional[List[Union[str, Image.Image]]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None
    ) -> Generator[str, None, None]:
        """Stream a response in a chat session.

        Args:
            message: The message to generate a response to.
            images: A list of images to include in the message.
            conversation_history: Previous conversation messages.

        Yields:
            Chunks of the generated response.
        """
        yield from self.model.stream_chat_generate(message, images, conversation_history)
