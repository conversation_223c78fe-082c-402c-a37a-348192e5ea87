"""
Execution Monitor for the AI Code Assistant.

This module monitors code execution in real-time, captures detailed metrics,
and provides comprehensive analysis of execution behavior.
"""

import time
import psutil
import threading
import subprocess
import logging
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
import json
import sys
import traceback

logger = logging.getLogger(__name__)

@dataclass
class ExecutionMetrics:
    """Metrics captured during code execution."""
    start_time: float
    end_time: float
    execution_time: float
    cpu_usage_percent: float
    memory_usage_mb: float
    peak_memory_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_io_sent_mb: float
    network_io_recv_mb: float
    exit_code: int
    stdout_lines: int
    stderr_lines: int
    exception_count: int
    warning_count: int

@dataclass
class ExecutionResult:
    """Result of code execution with comprehensive analysis."""
    success: bool
    output: str
    errors: List[str]
    warnings: List[str]
    metrics: ExecutionMetrics
    performance_analysis: Dict[str, Any]
    quality_indicators: Dict[str, Any]
    optimization_suggestions: List[str]
    execution_trace: List[Dict[str, Any]]

class ExecutionMonitor:
    """Monitors code execution and provides detailed analysis."""

    def __init__(self, workspace_dir: Path):
        """Initialize the execution monitor.
        
        Args:
            workspace_dir: The workspace directory
        """
        self.workspace_dir = workspace_dir
        self.active_processes: Dict[int, psutil.Process] = {}
        self.execution_history: List[ExecutionResult] = []
        self.monitoring_thread: Optional[threading.Thread] = None
        self.is_monitoring = False
        self.lock = threading.RLock()
        
        # Performance thresholds
        self.performance_thresholds = {
            "max_execution_time": 30.0,  # seconds
            "max_memory_usage": 512.0,   # MB
            "max_cpu_usage": 80.0,       # percent
            "max_disk_io": 100.0,        # MB
        }

    def execute_and_monitor(self, code: str, language: str, 
                           timeout: Optional[float] = None) -> ExecutionResult:
        """Execute code with comprehensive monitoring.
        
        Args:
            code: The code to execute
            language: The programming language
            timeout: Optional timeout in seconds
            
        Returns:
            Comprehensive execution result
        """
        with self.lock:
            logger.info(f"Starting monitored execution of {language} code")
            
            # Prepare execution environment
            temp_file = self._create_temp_file(code, language)
            command = self._get_execution_command(temp_file, language)
            
            # Initialize metrics
            start_time = time.time()
            initial_stats = self._get_system_stats()
            
            try:
                # Start execution with monitoring
                process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=self.workspace_dir
                )
                
                # Monitor the process
                execution_trace = []
                metrics = self._monitor_process(process, start_time, initial_stats, 
                                              execution_trace, timeout)
                
                # Get output
                stdout, stderr = process.communicate(timeout=timeout)
                
                # Analyze results
                result = self._analyze_execution(
                    process.returncode == 0,
                    stdout,
                    stderr,
                    metrics,
                    execution_trace,
                    code,
                    language
                )
                
                # Store in history
                self.execution_history.append(result)
                
                return result
                
            except subprocess.TimeoutExpired:
                logger.warning("Execution timed out")
                process.kill()
                return self._create_timeout_result(start_time)
                
            except Exception as e:
                logger.error(f"Execution failed: {e}")
                return self._create_error_result(str(e), start_time)
                
            finally:
                # Cleanup
                if temp_file.exists():
                    temp_file.unlink()

    def _create_temp_file(self, code: str, language: str) -> Path:
        """Create a temporary file for code execution.
        
        Args:
            code: The code content
            language: The programming language
            
        Returns:
            Path to the temporary file
        """
        extensions = {
            "python": ".py",
            "javascript": ".js",
            "typescript": ".ts",
            "java": ".java",
            "c": ".c",
            "cpp": ".cpp",
            "go": ".go",
            "rust": ".rs",
        }
        
        extension = extensions.get(language, ".txt")
        temp_file = self.workspace_dir / f"temp_execution_{int(time.time())}{extension}"
        
        with open(temp_file, "w", encoding="utf-8") as f:
            f.write(code)
        
        return temp_file

    def _get_execution_command(self, file_path: Path, language: str) -> List[str]:
        """Get the execution command for the given language.
        
        Args:
            file_path: Path to the code file
            language: The programming language
            
        Returns:
            Command list for execution
        """
        commands = {
            "python": ["python", str(file_path)],
            "javascript": ["node", str(file_path)],
            "typescript": ["npx", "ts-node", str(file_path)],
            "java": ["java", str(file_path)],
            "go": ["go", "run", str(file_path)],
            "rust": ["cargo", "run", "--manifest-path", str(file_path)],
        }
        
        return commands.get(language, ["cat", str(file_path)])

    def _get_system_stats(self) -> Dict[str, float]:
        """Get current system statistics.
        
        Returns:
            Dictionary of system stats
        """
        return {
            "cpu_percent": psutil.cpu_percent(),
            "memory_mb": psutil.virtual_memory().used / (1024 * 1024),
            "disk_read_mb": psutil.disk_io_counters().read_bytes / (1024 * 1024),
            "disk_write_mb": psutil.disk_io_counters().write_bytes / (1024 * 1024),
            "network_sent_mb": psutil.net_io_counters().bytes_sent / (1024 * 1024),
            "network_recv_mb": psutil.net_io_counters().bytes_recv / (1024 * 1024),
        }

    def _monitor_process(self, process: subprocess.Popen, start_time: float,
                        initial_stats: Dict[str, float], execution_trace: List[Dict[str, Any]],
                        timeout: Optional[float]) -> ExecutionMetrics:
        """Monitor a running process and collect metrics.
        
        Args:
            process: The subprocess to monitor
            start_time: Start time of execution
            initial_stats: Initial system statistics
            execution_trace: List to store execution trace
            timeout: Optional timeout
            
        Returns:
            Execution metrics
        """
        peak_memory = 0.0
        cpu_samples = []
        memory_samples = []
        
        try:
            ps_process = psutil.Process(process.pid)
            
            while process.poll() is None:
                if timeout and (time.time() - start_time) > timeout:
                    break
                
                try:
                    # Collect process metrics
                    cpu_percent = ps_process.cpu_percent()
                    memory_info = ps_process.memory_info()
                    memory_mb = memory_info.rss / (1024 * 1024)
                    
                    cpu_samples.append(cpu_percent)
                    memory_samples.append(memory_mb)
                    peak_memory = max(peak_memory, memory_mb)
                    
                    # Add to execution trace
                    execution_trace.append({
                        "timestamp": time.time(),
                        "cpu_percent": cpu_percent,
                        "memory_mb": memory_mb,
                        "status": ps_process.status(),
                    })
                    
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    break
                
                time.sleep(0.1)  # Sample every 100ms
                
        except Exception as e:
            logger.warning(f"Error monitoring process: {e}")
        
        # Calculate final metrics
        end_time = time.time()
        final_stats = self._get_system_stats()
        
        return ExecutionMetrics(
            start_time=start_time,
            end_time=end_time,
            execution_time=end_time - start_time,
            cpu_usage_percent=sum(cpu_samples) / len(cpu_samples) if cpu_samples else 0.0,
            memory_usage_mb=sum(memory_samples) / len(memory_samples) if memory_samples else 0.0,
            peak_memory_mb=peak_memory,
            disk_io_read_mb=final_stats["disk_read_mb"] - initial_stats["disk_read_mb"],
            disk_io_write_mb=final_stats["disk_write_mb"] - initial_stats["disk_write_mb"],
            network_io_sent_mb=final_stats["network_sent_mb"] - initial_stats["network_sent_mb"],
            network_io_recv_mb=final_stats["network_recv_mb"] - initial_stats["network_recv_mb"],
            exit_code=process.returncode or 0,
            stdout_lines=0,  # Will be calculated later
            stderr_lines=0,  # Will be calculated later
            exception_count=0,  # Will be calculated later
            warning_count=0,  # Will be calculated later
        )

    def _analyze_execution(self, success: bool, stdout: str, stderr: str,
                          metrics: ExecutionMetrics, execution_trace: List[Dict[str, Any]],
                          code: str, language: str) -> ExecutionResult:
        """Analyze execution results and provide insights.
        
        Args:
            success: Whether execution was successful
            stdout: Standard output
            stderr: Standard error
            metrics: Execution metrics
            execution_trace: Execution trace
            code: Original code
            language: Programming language
            
        Returns:
            Comprehensive execution result
        """
        # Parse output
        errors = self._parse_errors(stderr)
        warnings = self._parse_warnings(stderr)
        
        # Update metrics with output analysis
        metrics.stdout_lines = len(stdout.splitlines()) if stdout else 0
        metrics.stderr_lines = len(stderr.splitlines()) if stderr else 0
        metrics.exception_count = len([e for e in errors if "exception" in e.lower()])
        metrics.warning_count = len(warnings)
        
        # Performance analysis
        performance_analysis = self._analyze_performance(metrics, execution_trace)
        
        # Quality indicators
        quality_indicators = self._analyze_quality(code, metrics, errors, warnings, language)
        
        # Optimization suggestions
        optimization_suggestions = self._generate_optimization_suggestions(
            metrics, performance_analysis, quality_indicators
        )
        
        return ExecutionResult(
            success=success,
            output=stdout,
            errors=errors,
            warnings=warnings,
            metrics=metrics,
            performance_analysis=performance_analysis,
            quality_indicators=quality_indicators,
            optimization_suggestions=optimization_suggestions,
            execution_trace=execution_trace
        )

    def _parse_errors(self, stderr: str) -> List[str]:
        """Parse errors from stderr.
        
        Args:
            stderr: Standard error output
            
        Returns:
            List of parsed errors
        """
        if not stderr:
            return []
        
        errors = []
        lines = stderr.splitlines()
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ["error", "exception", "traceback"]):
                errors.append(line)
        
        return errors

    def _parse_warnings(self, stderr: str) -> List[str]:
        """Parse warnings from stderr.
        
        Args:
            stderr: Standard error output
            
        Returns:
            List of parsed warnings
        """
        if not stderr:
            return []
        
        warnings = []
        lines = stderr.splitlines()
        
        for line in lines:
            line = line.strip()
            if "warning" in line.lower():
                warnings.append(line)
        
        return warnings

    def _analyze_performance(self, metrics: ExecutionMetrics, 
                           execution_trace: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance metrics.
        
        Args:
            metrics: Execution metrics
            execution_trace: Execution trace
            
        Returns:
            Performance analysis
        """
        analysis = {
            "execution_time_score": self._score_execution_time(metrics.execution_time),
            "memory_efficiency_score": self._score_memory_usage(metrics.memory_usage_mb),
            "cpu_efficiency_score": self._score_cpu_usage(metrics.cpu_usage_percent),
            "io_efficiency_score": self._score_io_usage(metrics.disk_io_read_mb + metrics.disk_io_write_mb),
            "overall_performance_score": 0.0,
        }
        
        # Calculate overall score
        scores = [
            analysis["execution_time_score"],
            analysis["memory_efficiency_score"],
            analysis["cpu_efficiency_score"],
            analysis["io_efficiency_score"],
        ]
        analysis["overall_performance_score"] = sum(scores) / len(scores)
        
        # Performance trends
        if len(execution_trace) > 1:
            analysis["performance_trends"] = self._analyze_performance_trends(execution_trace)
        
        return analysis

    def _analyze_quality(self, code: str, metrics: ExecutionMetrics, 
                        errors: List[str], warnings: List[str], language: str) -> Dict[str, Any]:
        """Analyze code quality indicators.
        
        Args:
            code: Original code
            metrics: Execution metrics
            errors: List of errors
            warnings: List of warnings
            language: Programming language
            
        Returns:
            Quality indicators
        """
        return {
            "error_rate": len(errors) / max(metrics.stdout_lines + metrics.stderr_lines, 1),
            "warning_rate": len(warnings) / max(metrics.stdout_lines + metrics.stderr_lines, 1),
            "success_rate": 1.0 if metrics.exit_code == 0 else 0.0,
            "code_complexity": self._estimate_code_complexity(code, language),
            "maintainability_score": self._estimate_maintainability(code, language),
            "reliability_score": 1.0 - (len(errors) * 0.2 + len(warnings) * 0.1),
        }

    def _generate_optimization_suggestions(self, metrics: ExecutionMetrics,
                                         performance_analysis: Dict[str, Any],
                                         quality_indicators: Dict[str, Any]) -> List[str]:
        """Generate optimization suggestions.
        
        Args:
            metrics: Execution metrics
            performance_analysis: Performance analysis
            quality_indicators: Quality indicators
            
        Returns:
            List of optimization suggestions
        """
        suggestions = []
        
        # Performance suggestions
        if performance_analysis["execution_time_score"] < 0.7:
            suggestions.append("Consider optimizing algorithm complexity for better execution time")
        
        if performance_analysis["memory_efficiency_score"] < 0.7:
            suggestions.append("Optimize memory usage by reducing object allocations")
        
        if performance_analysis["cpu_efficiency_score"] < 0.7:
            suggestions.append("Reduce CPU-intensive operations or use parallel processing")
        
        # Quality suggestions
        if quality_indicators["error_rate"] > 0.1:
            suggestions.append("Add error handling to improve code reliability")
        
        if quality_indicators["maintainability_score"] < 0.7:
            suggestions.append("Refactor code to improve maintainability and readability")
        
        return suggestions

    def _score_execution_time(self, execution_time: float) -> float:
        """Score execution time performance."""
        max_time = self.performance_thresholds["max_execution_time"]
        return max(0.0, 1.0 - (execution_time / max_time))

    def _score_memory_usage(self, memory_mb: float) -> float:
        """Score memory usage efficiency."""
        max_memory = self.performance_thresholds["max_memory_usage"]
        return max(0.0, 1.0 - (memory_mb / max_memory))

    def _score_cpu_usage(self, cpu_percent: float) -> float:
        """Score CPU usage efficiency."""
        max_cpu = self.performance_thresholds["max_cpu_usage"]
        return max(0.0, 1.0 - (cpu_percent / max_cpu))

    def _score_io_usage(self, io_mb: float) -> float:
        """Score I/O usage efficiency."""
        max_io = self.performance_thresholds["max_disk_io"]
        return max(0.0, 1.0 - (io_mb / max_io))

    def _analyze_performance_trends(self, execution_trace: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance trends during execution."""
        if len(execution_trace) < 2:
            return {}
        
        cpu_values = [trace["cpu_percent"] for trace in execution_trace]
        memory_values = [trace["memory_mb"] for trace in execution_trace]
        
        return {
            "cpu_trend": "increasing" if cpu_values[-1] > cpu_values[0] else "decreasing",
            "memory_trend": "increasing" if memory_values[-1] > memory_values[0] else "decreasing",
            "peak_cpu": max(cpu_values),
            "peak_memory": max(memory_values),
        }

    def _estimate_code_complexity(self, code: str, language: str) -> float:
        """Estimate code complexity."""
        # Simple complexity estimation based on code structure
        lines = code.splitlines()
        complexity_indicators = ["if", "for", "while", "try", "except", "switch", "case"]
        
        complexity_count = 0
        for line in lines:
            for indicator in complexity_indicators:
                if indicator in line.lower():
                    complexity_count += 1
        
        return min(complexity_count / max(len(lines), 1), 1.0)

    def _estimate_maintainability(self, code: str, language: str) -> float:
        """Estimate code maintainability."""
        lines = code.splitlines()
        non_empty_lines = [line for line in lines if line.strip()]
        
        # Simple heuristics
        avg_line_length = sum(len(line) for line in non_empty_lines) / max(len(non_empty_lines), 1)
        comment_ratio = len([line for line in lines if line.strip().startswith("#")]) / max(len(lines), 1)
        
        # Score based on line length and comments
        length_score = max(0.0, 1.0 - (avg_line_length / 120))  # Prefer shorter lines
        comment_score = min(comment_ratio * 2, 1.0)  # Prefer more comments
        
        return (length_score + comment_score) / 2

    def _create_timeout_result(self, start_time: float) -> ExecutionResult:
        """Create result for timed out execution."""
        end_time = time.time()
        metrics = ExecutionMetrics(
            start_time=start_time,
            end_time=end_time,
            execution_time=end_time - start_time,
            cpu_usage_percent=0.0,
            memory_usage_mb=0.0,
            peak_memory_mb=0.0,
            disk_io_read_mb=0.0,
            disk_io_write_mb=0.0,
            network_io_sent_mb=0.0,
            network_io_recv_mb=0.0,
            exit_code=-1,
            stdout_lines=0,
            stderr_lines=0,
            exception_count=0,
            warning_count=0,
        )
        
        return ExecutionResult(
            success=False,
            output="",
            errors=["Execution timed out"],
            warnings=[],
            metrics=metrics,
            performance_analysis={"overall_performance_score": 0.0},
            quality_indicators={"success_rate": 0.0},
            optimization_suggestions=["Optimize code to reduce execution time"],
            execution_trace=[]
        )

    def _create_error_result(self, error_message: str, start_time: float) -> ExecutionResult:
        """Create result for failed execution."""
        end_time = time.time()
        metrics = ExecutionMetrics(
            start_time=start_time,
            end_time=end_time,
            execution_time=end_time - start_time,
            cpu_usage_percent=0.0,
            memory_usage_mb=0.0,
            peak_memory_mb=0.0,
            disk_io_read_mb=0.0,
            disk_io_write_mb=0.0,
            network_io_sent_mb=0.0,
            network_io_recv_mb=0.0,
            exit_code=-1,
            stdout_lines=0,
            stderr_lines=0,
            exception_count=1,
            warning_count=0,
        )
        
        return ExecutionResult(
            success=False,
            output="",
            errors=[error_message],
            warnings=[],
            metrics=metrics,
            performance_analysis={"overall_performance_score": 0.0},
            quality_indicators={"success_rate": 0.0},
            optimization_suggestions=["Fix execution errors"],
            execution_trace=[]
        )
