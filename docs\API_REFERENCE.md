# Autonomous AI Agent System - API Reference

## Overview

The Autonomous AI Agent System provides a comprehensive API for creating, managing, and monitoring autonomous AI agents that can handle complex tasks from start to completion.

## Core Classes

### AutonomousAgentController

Main controller for autonomous AI agent operations.

#### Constructor

```python
AutonomousAgentController(
    model_manager: <PERSON><PERSON>anager,
    conversation_manager: ConversationManager,
    workspace_dir: Path,
    tools: Dict[str, Any]
)
```

**Parameters:**
- `model_manager`: AI model management interface
- `conversation_manager`: Conversation history management
- `workspace_dir`: Working directory for agent operations
- `tools`: Dictionary of available tools for the agent

#### Methods

##### start_autonomous_session()

```python
start_autonomous_session(
    request: str,
    mode: AgentMode = AgentMode.AUTONOMOUS,
    constraints: Dict[str, Any] = None
) -> str
```

Start a new autonomous session.

**Parameters:**
- `request`: Initial task description
- `mode`: Operation mode (AUTONOMOUS, SEMI_AUTONOMOUS, INTERACTIVE)
- `constraints`: Optional constraints for execution

**Returns:** Session ID string

**Example:**
```python
session_id = controller.start_autonomous_session(
    "Create a web scraper for news articles",
    mode=AgentMode.AUTONOMOUS,
    constraints={"max_time": "30m", "language": "python"}
)
```

##### stop_autonomous_session()

```python
stop_autonomous_session() -> bool
```

Stop the current autonomous session.

**Returns:** True if stopped successfully, False otherwise

##### get_session_status()

```python
get_session_status() -> Dict[str, Any]
```

Get current session status and metrics.

**Returns:** Dictionary containing:
- `active`: Boolean indicating if session is active
- `session_id`: Current session identifier
- `mode`: Current operation mode
- `duration`: Session duration string
- `active_tasks_count`: Number of active tasks
- `total_tasks_completed`: Total completed tasks
- `active_tasks`: List of active task summaries

##### get_task_details()

```python
get_task_details(task_id: str) -> Optional[Dict[str, Any]]
```

Get detailed information about a specific task.

**Parameters:**
- `task_id`: Task identifier

**Returns:** Task details dictionary or None if not found

##### pause_current_task() / resume_current_task()

```python
pause_current_task() -> bool
resume_current_task() -> bool
```

Pause or resume the current autonomous task.

**Returns:** True if operation successful, False otherwise

##### Callback Methods

```python
set_progress_callback(callback: Callable)
set_status_callback(callback: Callable)
set_completion_callback(callback: Callable)
set_error_callback(callback: Callable)
```

Set callbacks for various autonomous operation events.

**Callback Data Formats:**

Progress Callback:
```python
{
    "type": "progress_update",
    "task_id": "task_uuid",
    "task_title": "Task name",
    "progress": 0.75,  # 0.0 to 1.0
    "status": "in_progress",
    "result": {
        "success": True,
        "confidence": 0.8,
        "execution_time": 2.5,
        "next_steps": ["step1", "step2"]
    }
}
```

Status Callback:
```python
{
    "type": "session_started" | "task_completed" | "decision_point",
    "session_id": "session_id",
    "task_id": "task_uuid",
    "task_title": "Task name",
    # Additional fields based on type
}
```

### AutonomousTask

Data structure representing an autonomous task.

#### Properties

```python
task_id: str                    # Unique task identifier
title: str                      # Task title
description: str                # Detailed task description
status: TaskStatus              # Current status
priority: TaskPriority          # Task priority level
created_at: datetime            # Creation timestamp
updated_at: datetime            # Last update timestamp
progress: float                 # Completion progress (0.0-1.0)
context: Dict[str, Any]         # Task context data
results: Dict[str, Any]         # Task execution results
next_steps: List[str]           # Planned next steps
success_criteria: List[str]     # Success criteria
checkpoints: List[Dict]         # Execution checkpoints
```

### AutonomousResearchEngine

Engine for autonomous research and information gathering.

#### Methods

##### conduct_research()

```python
async conduct_research(
    topic: str,
    depth: str = "comprehensive",
    focus_areas: List[str] = None
) -> ResearchResult
```

Conduct autonomous research on a topic.

**Parameters:**
- `topic`: Research topic
- `depth`: Research depth ("basic", "comprehensive", "deep")
- `focus_areas`: Optional focus areas for research

**Returns:** ResearchResult object

### IterativeImprovementSystem

System for iterative improvement of agent outputs.

#### Methods

##### improve_iteratively()

```python
improve_iteratively(
    content: str,
    content_type: str,
    success_criteria: List[str],
    context: Dict[str, Any] = None
) -> IterationResult
```

Improve content through iterative refinement.

**Parameters:**
- `content`: Content to improve
- `content_type`: Type of content ("code", "text", "documentation")
- `success_criteria`: List of success criteria
- `context`: Optional context information

**Returns:** IterationResult with improvement details

## Enumerations

### AgentMode

```python
class AgentMode(Enum):
    INTERACTIVE = "interactive"        # Standard user interaction
    AUTONOMOUS = "autonomous"          # Full autonomous operation
    SEMI_AUTONOMOUS = "semi_autonomous" # Autonomous with checkpoints
```

### TaskStatus

```python
class TaskStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"
    CANCELLED = "cancelled"
```

### TaskPriority

```python
class TaskPriority(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4
```

## Usage Examples

### Basic Autonomous Operation

```python
from core.autonomous_agent_controller import AutonomousAgentController
from core.autonomous_agent_framework import AgentMode

# Initialize controller
controller = AutonomousAgentController(
    model_manager=model_manager,
    conversation_manager=conversation_manager,
    workspace_dir=Path("./workspace"),
    tools=tools
)

# Set up monitoring
def progress_callback(data):
    print(f"Progress: {data['progress']*100:.1f}%")

def completion_callback(data):
    print(f"Task completed: {data['task_title']}")

controller.set_progress_callback(progress_callback)
controller.set_completion_callback(completion_callback)

# Start autonomous session
session_id = controller.start_autonomous_session(
    "Create a REST API for a todo application",
    mode=AgentMode.AUTONOMOUS
)

# Monitor execution
while True:
    status = controller.get_session_status()
    if not status['active']:
        break
    time.sleep(5)

print("Autonomous execution completed!")
```

### Research Integration

```python
from core.autonomous_agent_framework import AutonomousResearchEngine

# Initialize research engine
research_engine = AutonomousResearchEngine(
    web_tool=web_tool,
    search_tool=search_tool,
    rag_tool=rag_tool
)

# Conduct research
research_result = await research_engine.conduct_research(
    topic="machine learning best practices",
    depth="comprehensive",
    focus_areas=["neural networks", "data preprocessing"]
)

print(f"Research confidence: {research_result.confidence_score}")
print(f"Findings: {len(research_result.findings)}")
print(f"Sources: {len(research_result.sources)}")
```

### Iterative Improvement

```python
from core.iterative_improvement_system import IterativeImprovementSystem

# Initialize improvement system
improvement_system = IterativeImprovementSystem(
    model_manager=model_manager,
    tools=tools,
    workspace_dir=workspace_dir
)

# Improve code
code = """
def calculate(x, y):
    return x + y
"""

result = improvement_system.improve_iteratively(
    content=code,
    content_type="python",
    success_criteria=[
        "well_documented",
        "error_handling",
        "type_hints",
        "tested"
    ]
)

print(f"Improvement score: {result.overall_improvement_score}")
print(f"Iterations: {len(result.improvements_applied)}")
print("Improved code:")
print(result.improved_content)
```

### Task Management

```python
# Get active tasks
active_tasks = controller.persistence_manager.get_active_tasks()

for task in active_tasks:
    print(f"Task: {task.title}")
    print(f"Progress: {task.progress*100:.1f}%")
    print(f"Status: {task.status.value}")
    print(f"Next steps: {task.next_steps[:3]}")
    print()

# Get task details
task_details = controller.get_task_details(task.task_id)
if task_details:
    print(f"Created: {task_details['created_at']}")
    print(f"Checkpoints: {task_details['checkpoints']}")
    print(f"Success criteria: {task_details['success_criteria']}")
```

## Error Handling

### Common Exceptions

```python
try:
    session_id = controller.start_autonomous_session(request)
except Exception as e:
    print(f"Failed to start session: {e}")

# Check session status for errors
status = controller.get_session_status()
if 'error' in status:
    print(f"Session error: {status['error']}")
```

### Recovery Mechanisms

```python
# Pause on error
def error_callback(error):
    print(f"Error occurred: {error}")
    controller.pause_current_task()
    # Implement recovery logic
    controller.resume_current_task()

controller.set_error_callback(error_callback)
```

## Configuration

### Agent Configuration

```python
# Configure autonomous behavior
controller.execution_engine.max_execution_time = timedelta(hours=2)
controller.execution_engine.checkpoint_interval = timedelta(minutes=10)
controller.execution_engine.retry_limit = 3
controller.execution_engine.confidence_threshold = 0.7
```

### Research Configuration

```python
# Configure research depth
research_engine.research_cache = {}  # Clear cache
research_engine.research_history = []  # Clear history
```

### Improvement Configuration

```python
# Configure improvement system
improvement_system.max_iterations = 5
improvement_system.confidence_threshold = 0.8
improvement_system.improvement_threshold = 0.1
```

## Best Practices

1. **Always set callbacks** for monitoring autonomous operations
2. **Use appropriate timeouts** for long-running tasks
3. **Handle errors gracefully** with proper error callbacks
4. **Monitor resource usage** during autonomous execution
5. **Use semi-autonomous mode** for critical or sensitive tasks
6. **Regularly check task status** and progress
7. **Implement proper cleanup** when stopping sessions

## Troubleshooting

### Common Issues

1. **Session won't start**: Check model manager and tool availability
2. **Tasks not progressing**: Verify success criteria and constraints
3. **High resource usage**: Adjust execution parameters and timeouts
4. **Research failures**: Check web tool configuration and connectivity
5. **Improvement not working**: Verify content type and criteria

### Debug Information

```python
# Get detailed status
status = controller.get_session_status()
print(json.dumps(status, indent=2))

# Get improvement statistics
stats = improvement_system.get_improvement_statistics()
print(f"Success rate: {stats['success_rate']:.2f}")

# Get research history
history = controller.get_research_history()
for entry in history:
    print(f"Query: {entry['query']}, Confidence: {entry['confidence']}")
```
