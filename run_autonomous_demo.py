#!/usr/bin/env python3
"""
Complete Autonomous AI Agent System Demonstration

This script demonstrates the full capabilities of the autonomous AI agent system:
1. Autonomous task execution from start to completion
2. Comprehensive research and information gathering
3. Multi-step workflow planning and execution
4. Real-time progress monitoring and reporting
5. Iterative improvement and self-correction
6. Error handling and recovery mechanisms

Usage:
    python run_autonomous_demo.py
    python run_autonomous_demo.py --task "Your custom task"
    python run_autonomous_demo.py --mode semi_autonomous
"""

import sys
import os
import time
import argparse
import json
from pathlib import Path
from typing import Dict, Any

# Add the current directory to the path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

try:
    from models import ModelManager
    from conversation import ConversationManager
    from agent import Agent
    from config import load_config
    from utils import get_logger
    from core.autonomous_agent_framework import AgentMode
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure all dependencies are installed and the system is properly configured.")
    sys.exit(1)

logger = get_logger()

class AutonomousSystemDemo:
    """Complete demonstration of the autonomous AI agent system."""
    
    def __init__(self, custom_task: str = None, mode: str = "autonomous"):
        """Initialize the demonstration system."""
        print("🤖 Autonomous AI Agent System - Complete Demonstration")
        print("=" * 60)
        
        # Load configuration
        try:
            self.config = load_config()
            self.workspace_dir = Path(self.config.workspace_dir)
            print(f"✅ Configuration loaded successfully")
            print(f"📁 Workspace: {self.workspace_dir}")
        except Exception as e:
            print(f"❌ Failed to load configuration: {e}")
            sys.exit(1)
        
        # Initialize components
        try:
            print("🔧 Initializing AI Agent Components...")
            
            self.model_manager = ModelManager(
                provider=self.config.agent.provider,
                model_name=self.config.agent.model,
                temperature=self.config.agent.temperature,
                max_tokens=self.config.agent.max_tokens
            )
            
            self.conversation_manager = ConversationManager(self.config.history_dir)
            
            self.agent = Agent(
                model_manager=self.model_manager,
                conversation_manager=self.conversation_manager,
                workspace_dir=self.workspace_dir
            )
            
            print(f"✅ Agent initialized with {self.config.agent.provider}/{self.config.agent.model}")
            
        except Exception as e:
            print(f"❌ Failed to initialize agent: {e}")
            sys.exit(1)
        
        # Set demonstration parameters
        self.custom_task = custom_task
        self.mode = mode
        self.demo_tasks = [
            "Create a Python function that calculates the factorial of a number with proper error handling and documentation",
            "Research the latest trends in artificial intelligence and create a comprehensive summary report",
            "Design and implement a simple REST API for a todo application with user authentication",
            "Create a web scraper for news articles with data storage and analysis capabilities",
            "Develop a machine learning model for sentiment analysis with training and evaluation"
        ]
        
        # Check autonomous capabilities
        self.check_system_readiness()
    
    def check_system_readiness(self):
        """Check if the autonomous system is ready for operation."""
        print("\n🔍 Checking System Readiness...")
        
        # Check autonomous capabilities
        status = self.agent.get_autonomous_status()
        if status.get('available', False):
            print("✅ Autonomous capabilities available")
        else:
            print("❌ Autonomous capabilities not available")
            print("   Please ensure all dependencies are installed")
            sys.exit(1)
        
        # Check model connectivity
        try:
            test_response = self.model_manager.generate("Test connection")
            if test_response:
                print("✅ AI model connectivity verified")
            else:
                print("⚠️ AI model response empty, but connection established")
        except Exception as e:
            print(f"❌ AI model connectivity failed: {e}")
            sys.exit(1)
        
        # Check workspace
        if self.workspace_dir.exists() and self.workspace_dir.is_dir():
            print("✅ Workspace directory accessible")
        else:
            print("⚠️ Creating workspace directory...")
            self.workspace_dir.mkdir(parents=True, exist_ok=True)
            print("✅ Workspace directory created")
        
        print("🎉 System ready for autonomous operation!")
    
    def run_complete_demonstration(self):
        """Run the complete autonomous system demonstration."""
        print("\n🚀 Starting Complete Autonomous System Demonstration")
        print("=" * 60)
        
        try:
            if self.custom_task:
                # Run custom task
                self.demonstrate_custom_task()
            else:
                # Run predefined demonstrations
                self.demonstrate_simple_task()
                self.demonstrate_research_task()
                self.demonstrate_complex_workflow()
                self.demonstrate_monitoring_features()
                self.demonstrate_recovery_mechanisms()
            
            # Show final system status
            self.show_final_system_status()
            
            print("\n🎉 Complete Demonstration Finished Successfully!")
            print("The autonomous AI agent system is fully operational and ready for use.")
            
        except KeyboardInterrupt:
            print("\n⚠️ Demonstration interrupted by user")
            self.cleanup()
        except Exception as e:
            print(f"\n❌ Demonstration failed: {e}")
            logger.error(f"Demonstration failed: {e}")
            self.cleanup()
    
    def demonstrate_custom_task(self):
        """Demonstrate autonomous execution with custom task."""
        print(f"\n📋 Custom Task Demonstration")
        print("-" * 40)
        print(f"🎯 Task: {self.custom_task}")
        print(f"🤖 Mode: {self.mode}")
        
        self.execute_autonomous_task(self.custom_task, self.mode, timeout=300)
    
    def demonstrate_simple_task(self):
        """Demonstrate simple autonomous task execution."""
        print(f"\n📋 Demo 1: Simple Autonomous Task")
        print("-" * 40)
        
        task = self.demo_tasks[0]
        print(f"🎯 Task: {task}")
        
        self.execute_autonomous_task(task, "autonomous", timeout=120)
        self.wait_for_user_input()
    
    def demonstrate_research_task(self):
        """Demonstrate research-intensive autonomous task."""
        print(f"\n📋 Demo 2: Research-Intensive Task")
        print("-" * 40)
        
        task = self.demo_tasks[1]
        print(f"🎯 Task: {task}")
        print("🔍 This demonstrates autonomous research capabilities...")
        
        self.execute_autonomous_task(task, "autonomous", timeout=180)
        
        # Show research results
        self.show_research_results()
        self.wait_for_user_input()
    
    def demonstrate_complex_workflow(self):
        """Demonstrate complex multi-step workflow."""
        print(f"\n📋 Demo 3: Complex Multi-Step Workflow")
        print("-" * 40)
        
        task = self.demo_tasks[2]
        print(f"🎯 Task: {task}")
        print("⚙️ This demonstrates multi-step workflow planning and execution...")
        
        self.execute_autonomous_task(task, "autonomous", timeout=240, show_steps=True)
        
        # Show workflow breakdown
        self.show_workflow_breakdown()
        self.wait_for_user_input()
    
    def demonstrate_monitoring_features(self):
        """Demonstrate monitoring and control features."""
        print(f"\n📋 Demo 4: Monitoring & Control Features")
        print("-" * 40)
        
        task = self.demo_tasks[3]
        print(f"🎯 Task: {task}")
        print("📊 This demonstrates monitoring, pause/resume, and control features...")
        
        # Start task
        session_id = self.start_monitored_task(task)
        
        if session_id:
            # Monitor for a bit
            time.sleep(15)
            
            # Demonstrate pause
            print("⏸️ Demonstrating pause functionality...")
            if self.agent.pause_autonomous_task():
                print("✅ Task paused successfully")
                time.sleep(5)
                
                # Demonstrate resume
                print("▶️ Demonstrating resume functionality...")
                if self.agent.resume_autonomous_task():
                    print("✅ Task resumed successfully")
                else:
                    print("❌ Failed to resume task")
            else:
                print("❌ Failed to pause task")
            
            # Continue monitoring
            self.monitor_task_execution(timeout=60)
            
            # Stop task
            self.agent.stop_autonomous_mode()
        
        self.wait_for_user_input()
    
    def demonstrate_recovery_mechanisms(self):
        """Demonstrate error handling and recovery."""
        print(f"\n📋 Demo 5: Error Handling & Recovery")
        print("-" * 40)
        
        task = "Create a complex system that might encounter errors during execution"
        print(f"🎯 Task: {task}")
        print("🛡️ This demonstrates error handling and recovery mechanisms...")
        
        # Set up error monitoring
        errors_encountered = []
        
        def error_callback(error):
            errors_encountered.append(error)
            print(f"⚠️ Error handled: {error}")
        
        self.agent.set_autonomous_callbacks(error_callback=error_callback)
        
        # Execute task with potential for errors
        self.execute_autonomous_task(task, "autonomous", timeout=120)
        
        print(f"🛡️ Errors handled during execution: {len(errors_encountered)}")
        for i, error in enumerate(errors_encountered, 1):
            print(f"   {i}. {error}")
        
        self.wait_for_user_input()
    
    def execute_autonomous_task(self, task: str, mode: str, timeout: int = 120, show_steps: bool = False):
        """Execute an autonomous task with monitoring."""
        # Set up comprehensive monitoring
        self.setup_comprehensive_monitoring(show_steps)
        
        # Start autonomous session
        session_id = self.agent.start_autonomous_mode(task, mode)
        
        if session_id:
            print(f"✅ Autonomous session started: {session_id}")
            
            # Monitor execution
            self.monitor_task_execution(timeout)
            
            # Stop session
            self.agent.stop_autonomous_mode()
            print("🏁 Task execution completed")
        else:
            print("❌ Failed to start autonomous session")
    
    def start_monitored_task(self, task: str) -> str:
        """Start a task with monitoring setup."""
        self.setup_comprehensive_monitoring(show_steps=True)
        return self.agent.start_autonomous_mode(task, "autonomous")
    
    def setup_comprehensive_monitoring(self, show_steps: bool = False):
        """Set up comprehensive monitoring callbacks."""
        def progress_callback(data):
            task_title = data.get('task_title', 'Unknown')[:40]
            progress = data.get('progress', 0) * 100
            confidence = data.get('result', {}).get('confidence', 0) * 100
            print(f"  📈 Progress: {task_title}... - {progress:.1f}% (Confidence: {confidence:.1f}%)")
        
        def status_callback(data):
            status_type = data.get('type', 'unknown')
            if status_type == 'session_started':
                print(f"  🟢 Session started: {data.get('session_id', 'Unknown')}")
            elif status_type == 'task_completed':
                print(f"  ✅ Task completed: {data.get('task_title', 'Unknown')}")
            elif status_type == 'decision_point':
                print(f"  ⚠️ Decision point: {data.get('task_title', 'Unknown')}")
            elif status_type == 'status_update':
                status = data.get('status', {})
                active_tasks = status.get('active_tasks_count', 0)
                completed_tasks = status.get('total_tasks_completed', 0)
                if show_steps and active_tasks > 0:
                    print(f"  📊 Status: {active_tasks} active, {completed_tasks} completed")
        
        def completion_callback(data):
            print(f"  🎉 Task completed successfully!")
            print(f"     Task: {data.get('task_title', 'Unknown')}")
            print(f"     Final progress: {data.get('final_progress', 0)*100:.1f}%")
        
        def error_callback(error):
            print(f"  ❌ Error: {error}")
        
        self.agent.set_autonomous_callbacks(
            progress_callback=progress_callback,
            status_callback=status_callback,
            completion_callback=completion_callback,
            error_callback=error_callback
        )
    
    def monitor_task_execution(self, timeout: int = 120):
        """Monitor autonomous task execution."""
        start_time = time.time()
        last_status_time = 0
        
        print(f"⏱️ Monitoring execution (timeout: {timeout}s)...")
        
        while time.time() - start_time < timeout:
            status = self.agent.get_autonomous_status()
            
            if not status.get('active', False):
                print("  🏁 Execution completed")
                break
            
            # Show periodic detailed status
            if time.time() - last_status_time > 15:  # Every 15 seconds
                self.show_current_status(status)
                last_status_time = time.time()
            
            time.sleep(3)
        
        if time.time() - start_time >= timeout:
            print(f"  ⏰ Monitoring timeout reached ({timeout}s)")
    
    def show_current_status(self, status: Dict[str, Any]):
        """Show current execution status."""
        active_tasks = status.get('active_tasks_count', 0)
        completed_tasks = status.get('total_tasks_completed', 0)
        duration = status.get('duration', 'Unknown')
        
        print(f"  📊 Current Status:")
        print(f"     Duration: {duration}")
        print(f"     Active tasks: {active_tasks}")
        print(f"     Completed tasks: {completed_tasks}")
        
        if status.get('active_tasks'):
            task = status['active_tasks'][0]
            progress = task.get('progress', 0) * 100
            next_steps = task.get('next_steps', [])
            print(f"     Current progress: {progress:.1f}%")
            if next_steps:
                print(f"     Next: {next_steps[0][:50]}...")
    
    def show_research_results(self):
        """Show research results from autonomous execution."""
        research_history = self.agent.get_autonomous_research_history()
        
        if research_history:
            print(f"  🔍 Research conducted: {len(research_history)} queries")
            for i, research in enumerate(research_history[-3:], 1):  # Show last 3
                query = research.get('query', 'Unknown')[:50]
                confidence = research.get('confidence', 0)
                sources = research.get('sources_count', 0)
                print(f"    {i}. {query}... (Confidence: {confidence:.2f}, Sources: {sources})")
        else:
            print("  ℹ️ No research history available")
    
    def show_workflow_breakdown(self):
        """Show workflow breakdown and task details."""
        status = self.agent.get_autonomous_status()
        
        if status.get('active_tasks'):
            task = status['active_tasks'][0]
            task_id = task.get('task_id')
            
            if task_id:
                details = self.agent.get_autonomous_task_details(task_id)
                if details:
                    print(f"  📋 Workflow breakdown:")
                    print(f"     Progress: {details.get('progress', 0)*100:.1f}%")
                    print(f"     Next steps: {len(details.get('next_steps', []))}")
                    print(f"     Success criteria: {len(details.get('success_criteria', []))}")
                    print(f"     Checkpoints: {details.get('checkpoints', 0)}")
                    print(f"     Iterations: {details.get('iterations', 0)}")
    
    def show_final_system_status(self):
        """Show final system status and statistics."""
        print(f"\n📊 Final System Status")
        print("-" * 40)
        
        # Get session history
        session_history = self.agent.get_autonomous_session_history()
        research_history = self.agent.get_autonomous_research_history()
        
        print(f"📈 Session Statistics:")
        print(f"   Total sessions: {len(session_history)}")
        
        if session_history:
            latest = session_history[-1]
            print(f"   Latest session tasks: {latest.get('tasks_completed', 0)}")
            print(f"   Research queries: {latest.get('research_conducted', 0)}")
        
        print(f"🔍 Research Statistics:")
        print(f"   Total research queries: {len(research_history)}")
        
        if research_history:
            avg_confidence = sum(r.get('confidence', 0) for r in research_history) / len(research_history)
            print(f"   Average confidence: {avg_confidence:.2f}")
        
        # Show system capabilities
        capabilities = self.agent.get_autonomous_status()
        print(f"🤖 System Capabilities:")
        print(f"   Autonomous mode: {'✅ Available' if capabilities.get('available') else '❌ Not available'}")
        print(f"   Current status: {'🟢 Active' if capabilities.get('active') else '🔴 Inactive'}")
    
    def wait_for_user_input(self):
        """Wait for user input to continue."""
        if not self.custom_task:  # Only wait in demo mode
            try:
                input("\nPress Enter to continue to next demonstration...")
            except KeyboardInterrupt:
                raise
    
    def cleanup(self):
        """Clean up resources."""
        try:
            self.agent.stop_autonomous_mode()
            print("🧹 Cleanup completed")
        except Exception as e:
            logger.error(f"Cleanup error: {e}")

def main():
    """Main demonstration function."""
    parser = argparse.ArgumentParser(description="Autonomous AI Agent System Demonstration")
    parser.add_argument("--task", type=str, help="Custom task to execute")
    parser.add_argument("--mode", choices=["autonomous", "semi_autonomous", "interactive"], 
                       default="autonomous", help="Execution mode")
    
    args = parser.parse_args()
    
    try:
        demo = AutonomousSystemDemo(custom_task=args.task, mode=args.mode)
        demo.run_complete_demonstration()
        
    except KeyboardInterrupt:
        print("\n⚠️ Demonstration interrupted by user")
    except Exception as e:
        print(f"❌ Demonstration failed: {e}")
        logger.error(f"Demonstration failed: {e}")

if __name__ == "__main__":
    main()
