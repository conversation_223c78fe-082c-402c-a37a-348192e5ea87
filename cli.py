"""
CLI interface for the Advanced AI Agent.
"""

import os
import sys
import time
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple

import click
from prompt_toolkit import PromptSession
from prompt_toolkit.history import FileHistory
from prompt_toolkit.auto_suggest import AutoSuggestFromHistory
from prompt_toolkit.completion import <PERSON><PERSON>ompleter
from prompt_toolkit.styles import Style
from rich.console import Console
from rich.markdown import Markdown
from rich.syntax import Syntax
from rich.panel import Panel

from agent import Agent
from models import ModelManager
from conversation import ConversationManager
from config import load_config, save_config, get_config_dir
from utils import print_colored, print_error, print_success, print_info, print_warning, setup_logger

# Create a console for rich output
console = Console()

# Define the prompt style
style = Style.from_dict({
    "prompt": "bold #00aa00",
    "command": "bold #ffaa00",
})

# Define the command completer
command_completer = WordCompleter([
    "/help", "/exit", "/quit", "/history", "/list", "/load", "/new", "/delete",
    "/save", "/export", "/clear", "/models", "/config", "/debug", "/version",
    "/color", "/logging", "!"
])

def display_welcome_message() -> None:
    """Display the welcome message."""
    console.print(Panel.fit(
        "[bold blue]Advanced AI Agent[/bold blue]\n"
        "[green]A powerful terminal AI coding agent with Gemini API integration[/green]\n"
        "Type [bold yellow]/help[/bold yellow] for a list of commands.",
        title="Welcome",
        border_style="blue"
    ))

def display_help() -> None:
    """Display the help message."""
    console.print(Panel.fit(
        "Available commands:\n"
        "[bold yellow]/help[/bold yellow] - Show this help message\n"
        "[bold yellow]/exit[/bold yellow], [bold yellow]/quit[/bold yellow] - Exit the program\n"
        "[bold yellow]/history[/bold yellow] - Show the conversation history\n"
        "[bold yellow]/list[/bold yellow] - List all conversations\n"
        "[bold yellow]/load[/bold yellow] <id> - Load a conversation\n"
        "[bold yellow]/new[/bold yellow] <name> - Create a new conversation\n"
        "[bold yellow]/delete[/bold yellow] <id> - Delete a conversation\n"
        "[bold yellow]/save[/bold yellow] - Save the current conversation\n"
        "[bold yellow]/export[/bold yellow] <path> - Export the current conversation to a file\n"
        "[bold yellow]/clear[/bold yellow] - Clear the screen\n"
        "[bold yellow]/models[/bold yellow] - List available models\n"
        "[bold yellow]/config[/bold yellow] - Show the current configuration\n"
        "[bold yellow]/debug[/bold yellow] <on|off> - Enable or disable debug mode\n"
        "[bold yellow]/color[/bold yellow] <color> - Set the response text color (blue, green, cyan, etc.)\n"
        "[bold yellow]/logging[/bold yellow] <on|off> - Enable or disable console logging\n"
        "[bold yellow]/iterative[/bold yellow] <on|off> - Enable or disable iterative enhanced agent\n"
        "[bold yellow]/version[/bold yellow] - Show the version information\n\n"
        "[bold cyan]Autonomous Mode Commands:[/bold cyan]\n"
        "[bold yellow]/auto[/bold yellow] - Show autonomous mode status\n"
        "[bold yellow]/auto start[/bold yellow] <task> - Start autonomous mode with a task\n"
        "[bold yellow]/auto stop[/bold yellow] - Stop autonomous mode\n"
        "[bold yellow]/auto pause[/bold yellow] - Pause current autonomous task\n"
        "[bold yellow]/auto resume[/bold yellow] - Resume current autonomous task\n"
        "[bold yellow]/auto status[/bold yellow] - Show detailed autonomous status\n"
        "[bold yellow]/auto history[/bold yellow] - Show autonomous session history\n"
        "[bold yellow]/auto research[/bold yellow] - Show autonomous research history\n\n"
        "[bold yellow]![/bold yellow][command] - Execute a shell command directly (e.g., [bold yellow]!pwd && ls -l[/bold yellow])",
        title="Help",
        border_style="blue"
    ))

def format_response(response: str) -> None:
    """Format and display a response.

    Args:
        response: The response to format and display.
    """
    # Load config to get consistent color
    config = load_config()
    response_color = config.consistent_color

    # Check for code blocks
    lines = response.split("\n")
    in_code_block = False
    code_block_lang = ""
    code_block_content = []

    for i, line in enumerate(lines):
        if line.startswith("```") and not in_code_block:
            # Start of a code block
            in_code_block = True
            code_block_lang = line[3:].strip()
            code_block_content = []
        elif line.startswith("```") and in_code_block:
            # End of a code block
            in_code_block = False

            # Display the code block
            if code_block_content:
                try:
                    console.print(Syntax(
                        "\n".join(code_block_content),
                        code_block_lang or "text",
                        theme="monokai",
                        line_numbers=True
                    ))
                except Exception:
                    # Fallback to plain text if Syntax highlighting fails
                    console.print(f"[{response_color}]" + "\n".join(code_block_content) + f"[/{response_color}]")
        elif in_code_block:
            # Inside a code block
            code_block_content.append(line)
        else:
            # Regular text
            if i > 0 and not lines[i-1].startswith("```"):
                console.print(f"[{response_color}]{line}[/{response_color}]")

    # If we're still in a code block at the end, display it
    if in_code_block and code_block_content:
        try:
            console.print(Syntax(
                "\n".join(code_block_content),
                code_block_lang or "text",
                theme="monokai",
                line_numbers=True
            ))
        except Exception:
            # Fallback to plain text if Syntax highlighting fails
            console.print(f"[{response_color}]" + "\n".join(code_block_content) + f"[/{response_color}]")

def handle_command(command: str, agent: Agent) -> bool:
    """Handle a command.

    Args:
        command: The command to handle.
        agent: The agent to use.

    Returns:
        Whether to continue the conversation.
    """
    # Split the command into parts
    parts = command.split(maxsplit=1)
    cmd = parts[0].lower()
    args = parts[1] if len(parts) > 1 else ""

    if cmd in ["/exit", "/quit"]:
        # Exit the program
        return False

    elif cmd == "/help":
        # Show help
        display_help()

    elif cmd == "/auto":
        # Handle autonomous mode commands
        if not args:
            # Show autonomous status
            status = agent.get_autonomous_status()
            if status.get('available', False):
                if status.get('active', False):
                    console.print(Panel.fit(
                        f"[bold green]Autonomous Mode Active[/bold green]\n"
                        f"Session ID: {status.get('session_id', 'Unknown')}\n"
                        f"Mode: {status.get('mode', 'Unknown')}\n"
                        f"Duration: {status.get('duration', 'Unknown')}\n"
                        f"Active Tasks: {status.get('active_tasks_count', 0)}\n"
                        f"Tasks Completed: {status.get('total_tasks_completed', 0)}",
                        title="Autonomous Status",
                        border_style="green"
                    ))
                else:
                    console.print("[yellow]Autonomous mode available but not active.[/yellow]")
            else:
                console.print("[red]Autonomous mode not available.[/red]")
        else:
            # Handle autonomous subcommands
            auto_parts = args.split(maxsplit=1)
            auto_cmd = auto_parts[0].lower()
            auto_args = auto_parts[1] if len(auto_parts) > 1 else ""

            if auto_cmd == "start":
                if not auto_args:
                    console.print("[red]Please provide a task description.[/red]")
                    console.print("Usage: /auto start <task description>")
                else:
                    console.print(f"[cyan]Starting autonomous mode...[/cyan]")
                    session_id = agent.start_autonomous_mode(auto_args, "autonomous")
                    if session_id:
                        console.print(f"[green]✓ Autonomous session started: {session_id}[/green]")
                    else:
                        console.print("[red]Failed to start autonomous mode.[/red]")

            elif auto_cmd == "stop":
                console.print("[cyan]Stopping autonomous mode...[/cyan]")
                if agent.stop_autonomous_mode():
                    console.print("[green]✓ Autonomous mode stopped.[/green]")
                else:
                    console.print("[red]Failed to stop autonomous mode.[/red]")

            elif auto_cmd == "pause":
                if agent.pause_autonomous_task():
                    console.print("[yellow]⏸ Current task paused.[/yellow]")
                else:
                    console.print("[red]Failed to pause current task.[/red]")

            elif auto_cmd == "resume":
                if agent.resume_autonomous_task():
                    console.print("[green]▶ Current task resumed.[/green]")
                else:
                    console.print("[red]Failed to resume current task.[/red]")

            elif auto_cmd == "status":
                status = agent.get_autonomous_status()
                console.print(json.dumps(status, indent=2))

            elif auto_cmd == "history":
                history = agent.get_autonomous_session_history()
                if history:
                    console.print(Panel.fit(
                        "\n".join([
                            f"[bold blue]{i+1}.[/bold blue] {session['session_id']} "
                            f"({session['mode']}) - {session['tasks_completed']} tasks completed"
                            for i, session in enumerate(history)
                        ]),
                        title="Autonomous Session History",
                        border_style="blue"
                    ))
                else:
                    console.print("[yellow]No autonomous session history.[/yellow]")

            elif auto_cmd == "research":
                research = agent.get_autonomous_research_history()
                if research:
                    console.print(Panel.fit(
                        "\n".join([
                            f"[bold blue]{i+1}.[/bold blue] {r['query']} "
                            f"(Confidence: {r['confidence']:.2f}, Sources: {r['sources_count']})"
                            for i, r in enumerate(research)
                        ]),
                        title="Research History",
                        border_style="blue"
                    ))
                else:
                    console.print("[yellow]No research history.[/yellow]")

            else:
                console.print(f"[red]Unknown autonomous command: {auto_cmd}[/red]")
                console.print("Available commands: start, stop, pause, resume, status, history, research")

    elif cmd == "/history":
        # Show the conversation history
        if agent.conversation_manager.current_conversation:
            conversation = agent.conversation_manager.current_conversation
            console.print(Panel.fit(
                f"[bold blue]Conversation: {conversation.name}[/bold blue]\n"
                f"[green]ID: {conversation.id}[/green]\n"
                f"Created: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(conversation.created_at))}\n"
                f"Updated: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(conversation.updated_at))}\n"
                f"Messages: {len(conversation.messages)}",
                title="Conversation Info",
                border_style="blue"
            ))

            # Display the messages
            for message in conversation.messages:
                role_color = {
                    "user": "green",
                    "assistant": "blue",
                    "tool": "yellow",
                    "tool_result": "cyan"
                }.get(message.role, "white")

                console.print(f"[bold {role_color}]{message.role.upper()}:[/bold {role_color}]")
                console.print(message.content)
                console.print()
        else:
            print_warning("No conversation loaded.")

    elif cmd == "/list":
        # List all conversations
        conversations = agent.conversation_manager.list_conversations()

        if conversations:
            console.print(Panel.fit(
                "\n".join([
                    f"[bold blue]{i+1}.[/bold blue] [green]{c['name']}[/green] "
                    f"(ID: {c['id']}, Messages: {c['message_count']}, "
                    f"Updated: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(c['updated_at']))})"
                    for i, c in enumerate(conversations)
                ]),
                title=f"Conversations ({len(conversations)})",
                border_style="blue"
            ))
        else:
            print_warning("No conversations found.")

    elif cmd == "/load":
        # Load a conversation
        if not args:
            print_error("No conversation ID specified.")
            return True

        conversation = agent.conversation_manager.load_conversation(args)
        if conversation:
            print_success(f"Loaded conversation: {conversation.name}")
        else:
            print_error(f"Conversation not found: {args}")

    elif cmd == "/new":
        # Create a new conversation
        name = args or None
        conversation = agent.conversation_manager.new_conversation(name)
        print_success(f"Created new conversation: {conversation.name}")

    elif cmd == "/delete":
        # Delete a conversation
        if not args:
            print_error("No conversation ID specified.")
            return True

        if agent.conversation_manager.delete_conversation(args):
            print_success(f"Deleted conversation: {args}")
        else:
            print_error(f"Conversation not found: {args}")

    elif cmd == "/save":
        # Save the current conversation
        if agent.conversation_manager.current_conversation:
            agent.conversation_manager.save_conversation()
            print_success("Conversation saved.")
        else:
            print_warning("No conversation loaded.")

    elif cmd == "/export":
        # Export the current conversation
        if not args:
            print_error("No export path specified.")
            return True

        if agent.conversation_manager.current_conversation:
            try:
                export_path = Path(args)
                agent.conversation_manager.current_conversation.save(export_path.parent)
                print_success(f"Conversation exported to: {export_path}")
            except Exception as e:
                print_error(f"Error exporting conversation: {e}")
        else:
            print_warning("No conversation loaded.")

    elif cmd == "/clear":
        # Clear the screen
        os.system("cls" if os.name == "nt" else "clear")

    elif cmd == "/models":
        # List available models
        from config import get_available_models

        models = get_available_models()

        if models:
            console.print(Panel.fit(
                "\n".join([
                    f"[bold blue]{provider}:[/bold blue]\n" +
                    "\n".join([f"  [green]{model}[/green]" for model in provider_models])
                    for provider, provider_models in models.items()
                ]),
                title="Available Models",
                border_style="blue"
            ))
        else:
            print_warning("No models available.")

    elif cmd == "/config":
        # Show the current configuration
        config = load_config()

        console.print(Panel.fit(
            f"[bold blue]Agent:[/bold blue]\n"
            f"  Model: {config.agent.model}\n"
            f"  Provider: {config.agent.provider}\n"
            f"  Temperature: {config.agent.temperature}\n"
            f"  Max Tokens: {config.agent.max_tokens}\n\n"
            f"[bold blue]Directories:[/bold blue]\n"
            f"  History: {config.history_dir}\n"
            f"  Logs: {config.log_dir}\n"
            f"  Workspace: {config.workspace_dir or 'Not set'}\n\n"
            f"[bold blue]Display:[/bold blue]\n"
            f"  Response Color: {config.consistent_color}\n"
            f"  Silent Logging: {'Enabled' if config.silent_logging else 'Disabled'}\n\n"
            f"[bold blue]Debug:[/bold blue] {'Enabled' if config.debug else 'Disabled'}",
            title="Configuration",
            border_style="blue"
        ))

    elif cmd == "/debug":
        # Enable or disable debug mode
        if not args:
            print_error("No debug mode specified.")
            return True

        if args.lower() in ["on", "true", "yes", "1"]:
            config = load_config()
            config.debug = True
            save_config(config)
            print_success("Debug mode enabled.")
        elif args.lower() in ["off", "false", "no", "0"]:
            config = load_config()
            config.debug = False
            save_config(config)
            print_success("Debug mode disabled.")
        else:
            print_error(f"Invalid debug mode: {args}")

    elif cmd == "/version":
        # Show the version information
        import __init__
        __version__ = __init__.__version__

        console.print(Panel.fit(
            f"[bold blue]Advanced AI Agent[/bold blue] v{__version__}\n"
            f"[green]A powerful terminal AI coding agent with Gemini API integration[/green]",
            title="Version",
            border_style="blue"
        ))

    elif cmd == "/color":
        # Set the response text color
        if not args:
            print_error("No color specified.")
            return True

        # Valid colors
        valid_colors = ["black", "red", "green", "yellow", "blue", "magenta", "cyan", "white"]

        if args.lower() not in valid_colors:
            print_error(f"Invalid color: {args}. Valid colors are: {', '.join(valid_colors)}")
            return True

        # Update the configuration
        config = load_config()
        config.consistent_color = args.lower()
        save_config(config)
        print_success(f"Response text color set to: {args.lower()}")

    elif cmd == "/logging":
        # Enable or disable console logging
        if not args:
            print_error("No logging mode specified.")
            return True

        if args.lower() in ["on", "true", "yes", "1"]:
            config = load_config()
            config.silent_logging = False
            save_config(config)
            # Immediately update the logger
            setup_logger(config.log_dir, config.debug, silent=False)
            print_success("Console logging enabled.")
        elif args.lower() in ["off", "false", "no", "0"]:
            config = load_config()
            config.silent_logging = True
            save_config(config)
            # Immediately update the logger
            setup_logger(config.log_dir, config.debug, silent=True)
            print_success("Console logging disabled.")
        else:
            print_error(f"Invalid logging mode: {args}")

    elif cmd == "/iterative":
        # Enable or disable iterative enhanced agent
        if not args:
            print_error("No iterative mode specified.")
            return True

        if args.lower() in ["on", "true", "yes", "1"]:
            config = load_config()
            config.agent.tools["iterative"] = {"enabled": True}
            save_config(config)
            print_success("Iterative enhanced agent enabled.")
        elif args.lower() in ["off", "false", "no", "0"]:
            config = load_config()
            config.agent.tools["iterative"] = {"enabled": False}
            save_config(config)
            print_success("Iterative enhanced agent disabled.")
        else:
            print_error(f"Invalid iterative mode: {args}")

    else:
        # Unknown command
        print_error(f"Unknown command: {cmd}")

    return True

@click.command()
@click.option("--model", "-m", help="The model to use.")
@click.option("--provider", "-p", help="The provider to use.")
@click.option("--workspace", "-w", help="The workspace directory to use.")
@click.option("--debug", "-d", is_flag=True, help="Enable debug mode.")
@click.option("--version", "-v", is_flag=True, help="Show the version information.")
@click.option("--autonomous", "-a", is_flag=True, help="Start in autonomous mode.")
@click.option("--auto-mode", type=click.Choice(["autonomous", "semi_autonomous", "interactive"]),
              default="autonomous", help="Autonomous operation mode.")
@click.argument("message", required=False)
def cli(model: Optional[str], provider: Optional[str], workspace: Optional[str], debug: bool,
        version: bool, autonomous: bool, auto_mode: str, message: Optional[str]) -> None:
    """Advanced AI Agent - A powerful terminal AI coding agent with Gemini API integration."""
    # Show the version information if requested
    if version:
        import __init__
        __version__ = __init__.__version__

        console.print(Panel.fit(
            f"[bold blue]Advanced AI Agent[/bold blue] v{__version__}\n"
            f"[green]A powerful terminal AI coding agent with Gemini API integration[/green]",
            title="Version",
            border_style="blue"
        ))
        return

    # Load the configuration
    config = load_config()

    # Update the configuration with command-line options
    if model:
        config.agent.model = model
    if provider:
        config.agent.provider = provider
    if workspace:
        config.workspace_dir = Path(workspace)
    if debug:
        config.debug = True

    # Save the updated configuration
    save_config(config)

    # Set up the logger with silent mode from config
    logger = setup_logger(config.log_dir, config.debug, silent=config.silent_logging)

    # Create the model manager
    model_manager = ModelManager(
        provider=config.agent.provider,
        model_name=config.agent.model,
        temperature=config.agent.temperature,
        max_tokens=config.agent.max_tokens
    )

    # Create the conversation manager
    conversation_manager = ConversationManager(config.history_dir)

    # Create the agent
    agent = Agent(
        model_manager=model_manager,
        conversation_manager=conversation_manager,
        workspace_dir=config.workspace_dir
    )

    # Create a new conversation
    conversation_manager.new_conversation()

    # If a message was provided, process it and exit
    if message:
        if autonomous:
            # Start autonomous mode with the provided message
            console.print(f"[bold cyan]Starting Autonomous Mode ({auto_mode})...[/bold cyan]")
            console.print(f"[bold green]Initial Request:[/bold green] {message}")

            # Set up callbacks for autonomous mode
            def progress_callback(data):
                console.print(f"[cyan]Progress:[/cyan] {data.get('task_title', 'Unknown')} - {data.get('progress', 0)*100:.1f}%")

            def status_callback(data):
                if data.get('type') == 'session_started':
                    console.print(f"[green]✓ Autonomous session started: {data.get('session_id')}[/green]")
                elif data.get('type') == 'task_completed':
                    console.print(f"[green]✓ Task completed: {data.get('task_title')}[/green]")
                elif data.get('type') == 'decision_point':
                    console.print(f"[yellow]⚠ Decision point reached in task: {data.get('task_title')}[/yellow]")

            def completion_callback(data):
                console.print(f"[bold green]🎉 Task completed successfully![/bold green]")
                console.print(f"Task: {data.get('task_title')}")
                console.print(f"Final Progress: {data.get('final_progress', 0)*100:.1f}%")

            def error_callback(error):
                console.print(f"[bold red]❌ Error: {error}[/bold red]")

            # Set callbacks
            agent.set_autonomous_callbacks(
                progress_callback=progress_callback,
                status_callback=status_callback,
                completion_callback=completion_callback,
                error_callback=error_callback
            )

            # Start autonomous session
            session_id = agent.start_autonomous_mode(message, auto_mode)

            if session_id:
                console.print(f"[bold green]Autonomous mode started successfully![/bold green]")
                console.print(f"Session ID: {session_id}")
                console.print("[dim]The agent will work autonomously. Press Ctrl+C to stop.[/dim]")

                try:
                    # Monitor autonomous execution
                    while True:
                        status = agent.get_autonomous_status()
                        if not status.get('active', False):
                            console.print("[yellow]Autonomous session completed.[/yellow]")
                            break

                        time.sleep(2)  # Check every 2 seconds

                except KeyboardInterrupt:
                    console.print("\n[yellow]Stopping autonomous mode...[/yellow]")
                    agent.stop_autonomous_mode()
                    console.print("[green]Autonomous mode stopped.[/green]")
            else:
                console.print("[red]Failed to start autonomous mode.[/red]")

            return
        else:
            response = agent.process_message(message)
            format_response(response)
            return

    # Display the welcome message
    display_welcome_message()

    # Create the prompt session
    history_file = get_config_dir() / "history.txt"
    session = PromptSession(
        history=FileHistory(str(history_file)),
        auto_suggest=AutoSuggestFromHistory(),
        completer=command_completer,
        style=style
    )

    # Main loop
    while True:
        try:
            # Get the user input
            user_input = session.prompt(
                [("class:prompt", ">>> ")],
                style=style
            )

            # Skip empty input
            if not user_input.strip():
                continue

            # Handle commands
            if user_input.startswith("/"):
                if not handle_command(user_input, agent):
                    break
                continue

            # Handle direct shell commands
            if user_input.startswith("!"):
                # Extract the shell command (remove the ! prefix)
                shell_command = user_input[1:].strip()
                if shell_command:
                    try:
                        # First, try using the platform's native shell directly
                        is_windows = os.name == 'nt'

                        if is_windows:
                            # On Windows, try cmd.exe first for complex commands
                            if any(char in shell_command for char in "|&><;"):
                                try:
                                    process = subprocess.run(
                                        ["cmd.exe", "/c", shell_command],
                                        stdout=subprocess.PIPE,
                                        stderr=subprocess.PIPE,
                                        universal_newlines=True,
                                        cwd=os.getcwd()
                                    )
                                    stdout = process.stdout
                                    stderr = process.stderr
                                    return_code = process.returncode
                                except Exception as e:
                                    # Fall back to the shell tool
                                    logger.warning(f"Failed to execute with cmd.exe: {e}")
                                    stdout, stderr, return_code = agent.shell_tool.execute(shell_command)
                            else:
                                # For simple commands, use the shell tool
                                stdout, stderr, return_code = agent.shell_tool.execute(shell_command)
                        else:
                            # On Unix-like systems, try bash directly
                            try:
                                process = subprocess.run(
                                    ["bash", "-c", shell_command],
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE,
                                    universal_newlines=True,
                                    cwd=os.getcwd()
                                )
                                stdout = process.stdout
                                stderr = process.stderr
                                return_code = process.returncode
                            except Exception as e:
                                # Fall back to the shell tool
                                logger.warning(f"Failed to execute with bash: {e}")
                                stdout, stderr, return_code = agent.shell_tool.execute(shell_command)

                        # Display the results
                        if return_code == 0:
                            if stdout.strip():
                                console.print(f"[bold green]Command executed successfully:[/bold green]")
                                console.print(stdout)
                            else:
                                console.print("[bold green]Command executed successfully.[/bold green]")
                        else:
                            console.print(f"[bold red]Command failed with return code {return_code}:[/bold red]")
                            if stderr.strip():
                                console.print(stderr)
                    except Exception as e:
                        # If all else fails, try one more approach with the system shell
                        try:
                            # Use the system shell directly as a last resort
                            process = subprocess.run(
                                shell_command,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                shell=True,
                                universal_newlines=True,
                                cwd=os.getcwd()
                            )
                            stdout = process.stdout
                            stderr = process.stderr
                            return_code = process.returncode

                            # Display the results
                            if return_code == 0:
                                if stdout.strip():
                                    console.print(f"[bold green]Command executed successfully:[/bold green]")
                                    console.print(stdout)
                                else:
                                    console.print("[bold green]Command executed successfully.[/bold green]")
                            else:
                                console.print(f"[bold red]Command failed with return code {return_code}:[/bold red]")
                                if stderr.strip():
                                    console.print(stderr)
                        except Exception as final_e:
                            console.print(f"[bold red]Error executing command: {final_e}[/bold red]")
                else:
                    print_error("No command specified after '!'")
                continue

            # Process the user input
            console.print("[bold green]Thinking...[/bold green]")

            # Get consistent color from config
            response_color = config.consistent_color

            # Check if iterative enhanced agent is enabled
            use_iterative = config.agent.tools.get("iterative", {}).get("enabled", False)
            if use_iterative:
                console.print("[bold cyan]Using Iterative Enhanced Agent...[/bold cyan]")

            # Stream the response
            console.print(f"[bold {response_color}]Assistant:[/bold {response_color}]")
            for chunk in agent.stream_process_message(user_input, use_iterative_agent=use_iterative):
                console.print(f"[{response_color}]{chunk}[/{response_color}]", end="")
            console.print()

        except KeyboardInterrupt:
            # Exit on Ctrl+C
            break

        except Exception as e:
            print_error(f"Error: {e}")
            if config.debug:
                import traceback
                traceback.print_exc()

    print_info("Goodbye!")

if __name__ == "__main__":
    cli()
